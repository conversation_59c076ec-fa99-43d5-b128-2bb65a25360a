@import 'tailwindcss';

@plugin "@tailwindcss/typography";
@plugin "@tailwindcss/forms";

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --color-primary-300: #639be9;
    --color-primary-400: #5893e7;
    --color-primary-500: #4d8ce6;
    --color-primary-600: #3c81e3;
    --color-secondary: #3f3f3f;
    --color-success-500: #44cf6e;
    --color-success-600: #0cb54f;
    --color-error-400: var(--color-red-400);
    --color-error-500: var(--color-red-500);
    --color-error-600: var(--color-red-600);
    --color-error-700: var(--color-red-700);
    --color-warning-600: var(--color-orange-600);
    --color-info-600: var(--color-blue-600);
    --color-light-base-50: #ffffff;
    --color-light-base-100: #fcfcfc;
    --color-light-base-200: #f6f7f8;
    --color-light-base-300: #ebedf0;
    --color-light-base-400: #e2e5e9;
    --color-light-base-500: #d8d9da;
    --color-light-base-600: #afb5bd;
    --color-light-base-700: #6e767e;
    --color-light-base-800: #6d757d;
    --color-light-base-900: #555e68;
    --color-light-base-950: #222222;
    --color-base-50: #dadada;
    --color-base-100: #bec6cf;
    --color-base-200: #a8afb8;
    --color-base-300: #a5adb5;
    --color-base-400: #536170;
    --color-base-500: #35393e;
    --color-base-600: #34383b;
    --color-base-700: #383c43;
    --color-base-800: #282c34;
    --color-base-900: #1c2127;
    --color-base-950: #181c20;
}

@layer base {
    * {
        scrollbar-width: thin;
    }

    button:not(:disabled),
    [role="button"]:not(:disabled) {
        cursor: pointer;
    }

    .prose {
        a {
            color: var(--color-primary-400) !important;
            cursor: pointer;
        }

        a:hover {
            color: var(--color-primary-300) !important;
        }

        @media (prefers-color-scheme: dark) {
            a {
                color: var(--color-primary-500) !important;
            }

            a:hover {
                color: var(--color-primary-600) !important;
            }
        }

        hr {
            margin-top: 2em !important;
            margin-bottom: 2em !important;
        }

        blockquote {
            p {
                &:first-of-type::before,
                &:last-of-type::after {
                    content: none !important;
                }
            }
        }

        ul,
        ol {
            margin: 1.25rem 1rem 1.25rem 0.4rem;

            li {
                margin-top: 0.3em !important;
                margin-bottom: 0.3em !important;
                padding-left: 0 !important;
            }

            li p {
                margin-top: 0 !important;
                margin-bottom: 0 !important;
            }
        }

        ul[data-type="taskList"] {
            margin-left: 0;
            padding-inline-start: 0.70rem !important;

            li {
                display: flex;
                padding-inline-start: 0 !important;

                > label {
                    margin-top: -0.1rem;
                    margin-right: 0.5rem;
                }

                > div > p {
                    margin-top: 0 !important;
                    margin-bottom: 0 !important;
                }

                input[type="checkbox"] {
                    cursor: pointer;
                    border-radius: 4px;
                }
            }

            li[data-checked="true"] {
                p {
                    opacity: 0.7;

                    @media (prefers-color-scheme: dark) {
                        opacity: 0.5;
                    }
                }
            }
        }

        table {
            th {
                font-weight: bold;
            }

            p {
                margin: 0 !important;
            }
        }

        pre {
            background-color: var(--color-light-base-900) !important;

            @media (prefers-color-scheme: dark) {
                background-color: var(--color-base-900) !important;
            }

            .hljs-comment,
            .hljs-quote {
                color: #616161;
            }

            .hljs-variable,
            .hljs-template-variable,
            .hljs-attribute,
            .hljs-tag,
            .hljs-name,
            .hljs-regexp,
            .hljs-link,
            .hljs-selector-id,
            .hljs-selector-class {
                color: #F98181;
            }

            .hljs-number,
            .hljs-meta,
            .hljs-built_in,
            .hljs-builtin-name,
            .hljs-literal,
            .hljs-type,
            .hljs-params {
                color: #FBBC88;
            }

            .hljs-string,
            .hljs-symbol,
            .hljs-bullet {
                color: #B9F18D;
            }

            .hljs-title,
            .hljs-section {
                color: #FAF594;
            }

            .hljs-keyword,
            .hljs-selector-tag {
                color: #70CFF8;
            }

            .hljs-emphasis {
                font-style: italic;
            }

            .hljs-strong {
                font-weight: 700;
            }
        }
    }
}