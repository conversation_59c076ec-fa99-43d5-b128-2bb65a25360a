#!/usr/bin/env python3
"""
Test script to confirm the FileController bug
The controller uses $request->vault instead of the injected $vault parameter
"""

import requests
import sys

def test_controller_bug(base_url, vault_id, session_cookie):
    """Test if the controller bug is causing the issues"""
    print(f"[+] Testing FileController bug for vault {vault_id}")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Test basic file access (should fail due to $request->vault bug)
    test_url = f"{base_url}/files/{vault_id}?path=test.md"
    
    print(f"[+] Testing URL: {test_url}")
    
    try:
        response = requests.get(test_url, headers=headers, timeout=10)
        
        print(f"[+] Status Code: {response.status_code}")
        
        if response.status_code == 500:
            print("[CONFIRMED] 500 error - likely due to $request->vault bug")
            print("[INFO] The controller tries to access $request->vault which doesn't exist")
            print("[INFO] It should use the injected $vault parameter instead")
            
            # Check if error contains clues
            if response.text:
                error_preview = response.text[:500]
                print(f"[ERROR PREVIEW] {error_preview}")
                
                if 'vault' in error_preview.lower():
                    print("[CONFIRMED] Error mentions 'vault' - confirms the bug")
        
        elif response.status_code == 404:
            print("[INFO] 404 - File not found (normal if file doesn't exist)")
        elif response.status_code == 403:
            print("[INFO] 403 - Authorization failed")
        elif response.status_code == 200:
            print("[SUCCESS] 200 - File served successfully")
        else:
            print(f"[INFO] Unexpected status: {response.status_code}")
            
    except Exception as e:
        print(f"[ERROR] Request failed: {e}")

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 test_controller_bug.py <base_url> <vault_id> <session_cookie>")
        print("\nThis script tests if the FileController bug is causing issues.")
        print("The bug: Line 24 uses $request->vault instead of injected $vault")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("=" * 60)
    print("FILECONTROLLER BUG TEST")
    print("=" * 60)
    print("Testing if the controller bug is causing the 500 errors")
    print("Bug: Gate::authorize('view', $request->vault) should be")
    print("     Gate::authorize('view', $vault)")
    print("=" * 60)
    
    test_controller_bug(base_url, vault_id, session_cookie)
    
    print("\n" + "=" * 60)
    print("CONCLUSION")
    print("=" * 60)
    print("If you see a 500 error, the bug is confirmed.")
    print("The fix is to change line 24 in FileController.php:")
    print("  FROM: Gate::authorize('view', $request->vault);")
    print("  TO:   Gate::authorize('view', $vault);")

if __name__ == "__main__":
    main()
