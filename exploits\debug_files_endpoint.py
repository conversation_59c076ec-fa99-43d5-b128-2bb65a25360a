#!/usr/bin/env python3
"""
Debug script for Many Notes /files endpoint
Helps identify why the endpoint returns 404
"""

import requests
import sys
import json
from urllib.parse import quote

def debug_authentication(base_url, session_cookie):
    """Check if authentication is working"""
    print("[+] Testing authentication...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Test dashboard access (requires auth)
    dashboard_url = f"{base_url}/"
    response = requests.get(dashboard_url, headers=headers, allow_redirects=False)
    
    if response.status_code == 302 and 'login' in response.headers.get('Location', ''):
        print("[ERROR] Authentication failed - redirected to login")
        return False
    elif response.status_code == 200:
        print("[SUCCESS] Authentication working")
        return True
    else:
        print(f"[WARNING] Unexpected response: {response.status_code}")
        return False

def get_available_vaults(base_url, session_cookie):
    """Get list of available vaults"""
    print("[+] Getting available vaults...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    vaults_url = f"{base_url}/vaults"
    response = requests.get(vaults_url, headers=headers)
    
    if response.status_code == 200:
        # Try to extract vault IDs from the page
        import re
        vault_links = re.findall(r'/vaults/(\d+)', response.text)
        vault_ids = list(set(vault_links))  # Remove duplicates
        
        if vault_ids:
            print(f"[SUCCESS] Found vault IDs: {vault_ids}")
            return vault_ids
        else:
            print("[INFO] No vault IDs found in page")
            return []
    else:
        print(f"[ERROR] Cannot access vaults page: {response.status_code}")
        return []

def test_vault_access(base_url, vault_id, session_cookie):
    """Test if we can access a specific vault"""
    print(f"[+] Testing access to vault {vault_id}...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    vault_url = f"{base_url}/vaults/{vault_id}"
    response = requests.get(vault_url, headers=headers)
    
    if response.status_code == 200:
        print(f"[SUCCESS] Can access vault {vault_id}")
        return True
    elif response.status_code == 404:
        print(f"[ERROR] Vault {vault_id} not found")
        return False
    elif response.status_code == 403:
        print(f"[ERROR] Access denied to vault {vault_id}")
        return False
    else:
        print(f"[ERROR] Unexpected response: {response.status_code}")
        return False

def test_files_endpoint_variations(base_url, vault_id, session_cookie):
    """Test different variations of the files endpoint"""
    print(f"[+] Testing files endpoint variations for vault {vault_id}...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    test_cases = [
        # Basic endpoint without path (should return 404 per controller logic)
        f"/files/{vault_id}",
        
        # With empty path
        f"/files/{vault_id}?path=",
        
        # With simple path
        f"/files/{vault_id}?path=/test.txt",
        
        # With traversal path
        f"/files/{vault_id}?path=../../../etc/passwd",
        
        # URL encoded traversal
        f"/files/{vault_id}?path={quote('../../../etc/passwd')}",
    ]
    
    for test_case in test_cases:
        url = base_url + test_case
        print(f"\n[+] Testing: {test_case}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 404:
                print("    Result: 404 - Expected for missing path or file not found")
            elif response.status_code == 403:
                print("    Result: 403 - Access denied (authorization failed)")
            elif response.status_code == 401:
                print("    Result: 401 - Authentication required")
            elif response.status_code == 200:
                content_preview = response.text[:100].replace('\n', '\\n')
                print(f"    Result: 200 - SUCCESS! Content: {content_preview}...")
            elif response.status_code == 500:
                print("    Result: 500 - Server error (possible path issue)")
            else:
                print(f"    Result: {response.status_code} - Unexpected")
                
        except requests.RequestException as e:
            print(f"    Error: {e}")

def check_laravel_routes(base_url, session_cookie):
    """Try to access Laravel route list if available"""
    print("[+] Checking if route debugging is available...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Common Laravel debug endpoints (usually disabled in production)
    debug_endpoints = [
        "/_debugbar",
        "/telescope",
        "/horizon",
        "/routes",  # Custom route list endpoint
    ]
    
    for endpoint in debug_endpoints:
        url = base_url + endpoint
        try:
            response = requests.get(url, headers=headers, timeout=5)
            if response.status_code == 200:
                print(f"[INFO] Debug endpoint available: {endpoint}")
        except:
            pass

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 debug_files_endpoint.py <base_url> <session_cookie> [vault_id]")
        print("\nExample:")
        print("  python3 debug_files_endpoint.py http://localhost:8000 your_session_cookie_here")
        print("  python3 debug_files_endpoint.py http://localhost:8000 your_session_cookie_here 1")
        print("\nTo get your session cookie:")
        print("  1. Login to the application in your browser")
        print("  2. Open Developer Tools (F12)")
        print("  3. Go to Application/Storage tab")
        print("  4. Find 'laravel_session' cookie value")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    session_cookie = sys.argv[2]
    vault_id = sys.argv[3] if len(sys.argv) > 3 else None
    
    print("=" * 60)
    print("MANY NOTES /FILES ENDPOINT DEBUG")
    print("=" * 60)
    
    # Step 1: Check authentication
    if not debug_authentication(base_url, session_cookie):
        print("\n[CRITICAL] Authentication failed. Please check your session cookie.")
        return
    
    # Step 2: Get available vaults
    vault_ids = get_available_vaults(base_url, session_cookie)
    
    if not vault_ids and not vault_id:
        print("\n[ERROR] No vaults found and no vault ID provided")
        print("Try creating a vault first or specify a vault ID manually")
        return
    
    # Step 3: Test vault access
    test_vault_id = vault_id if vault_id else vault_ids[0]
    
    if not test_vault_access(base_url, test_vault_id, session_cookie):
        print(f"\n[ERROR] Cannot access vault {test_vault_id}")
        if vault_ids:
            print(f"Try one of these vault IDs: {vault_ids}")
        return
    
    # Step 4: Test files endpoint
    test_files_endpoint_variations(base_url, test_vault_id, session_cookie)
    
    # Step 5: Check for debug endpoints
    check_laravel_routes(base_url, session_cookie)
    
    print("\n" + "=" * 60)
    print("DEBUG SUMMARY")
    print("=" * 60)
    print(f"Base URL: {base_url}")
    print(f"Tested Vault ID: {test_vault_id}")
    print(f"Available Vault IDs: {vault_ids}")
    print("\nIf all tests show 404:")
    print("1. Make sure the vault exists and you have access")
    print("2. Check that Laravel routes are properly loaded")
    print("3. Verify the application is running on the correct port")
    print("4. Try accessing a legitimate file first through the web interface")

if __name__ == "__main__":
    main()
