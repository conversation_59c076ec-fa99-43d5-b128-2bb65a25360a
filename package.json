{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.5", "autoprefixer": "^10.4.21", "axios": "^1.9.0", "laravel-echo": "^2.0.2", "laravel-vite-plugin": "^1.2.0", "pusher-js": "^8.4.0", "tailwindcss": "^4.1.5", "vite": "^6.3.4"}, "dependencies": {"@guyplusplus/turndown-plugin-gfm": "^1.0.7", "@tiptap/core": "^2.12.0", "@tiptap/extension-code-block-lowlight": "^2.12.0", "@tiptap/extension-image": "^2.12.0", "@tiptap/extension-link": "^2.12.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.12.0", "@tiptap/extension-task-list": "^2.12.0", "@tiptap/pm": "^2.12.0", "@tiptap/starter-kit": "^2.12.0", "dompurify": "^3.2.5", "lowlight": "^3.3.0", "marked": "^15.0.11", "turndown": "^7.2.0"}}