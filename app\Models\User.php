<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

final class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;

    use Notifiable;

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the associated vaults.
     *
     * @return HasMany<Vault, $this>
     */
    public function vaults(): Has<PERSON>any
    {
        return $this->hasMany(Vault::class, 'created_by');
    }

    /**
     * Get the collaborations that belongs to the user.
     *
     * @return BelongsToMany<Vault, $this>
     */
    public function collaborations(): BelongsToMany
    {
        return $this->belongsToMany(Vault::class)->withPivot('accepted');
    }

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }
}
