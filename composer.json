{"name": "brufdev/many-notes", "description": "Markdown note-taking web application designed for simplicity ", "version": "0.10.1", "type": "project", "keywords": ["notes", "<PERSON><PERSON>", "note management", "Markdown support", "Markdown editor", "filesystem storage", "database storage", "authentication", "responsive design", "SPA", "self-hosting", "privacy-focused", "open source", "PHP", "lightweight"], "homepage": "https://github.com/brufdev/many-notes", "license": "MIT", "require": {"php": "^8.4", "laravel/framework": "^12.17.0", "laravel/reverb": "^1.5", "laravel/scout": "^10.15.0", "laravel/socialite": "^5.21", "laravel/tinker": "^2.10.1", "livewire/livewire": "^3.6.3", "socialiteproviders/authelia": "^4.0", "socialiteproviders/authentik": "^5.2", "socialiteproviders/keycloak": "^5.3", "socialiteproviders/zitadel": "^4.1", "staudenmeir/laravel-adjacency-list": "^1.25.1", "typesense/typesense-php": "^5.1"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.15.4", "calebdw/larastan-livewire": "^2.2", "fakerphp/faker": "^1.24.1", "larastan/larastan": "^3.4", "laravel/pint": "^1.22.1", "laravel/sail": "^1.43.1", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.8", "pestphp/pest": "^3.8.2", "pestphp/pest-plugin-type-coverage": "^3.5.1", "rector/rector": "^2.0.17", "squizlabs/php_codesniffer": "^3.13.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "test:refactor": "rector --dry-run", "test:lint": "pint --test", "test:types": "phpstan", "test:coding-standards": "phpcs --standard=PSR12 app database/factories database/migrations database/seeders routes tests/Feature tests/Unit", "test:type-coverage": "pest --type-coverage --min=100", "test:unit": "pest --parallel", "test": ["@test:refactor", "@test:lint", "@test:coding-standards", "@test:types", "@test:type-coverage", "@test:unit"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}